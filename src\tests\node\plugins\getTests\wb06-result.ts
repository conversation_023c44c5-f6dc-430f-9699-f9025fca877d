export default {
  tests: {
    Sheet1: [
      {
        Id: "2",
        Run: "z",
        Test: "1",
        Steps: [
          { do: "x", action: "Start Test" },
          { do: "x", action: "Action1", rowNumber: "5" },
          { do: "x", action: "End Test" },
        ],
      },
      {
        Id: "7",
        Run: "z",
        Test: "1",
        Steps: [
          {
            do: "x",
            action: "Start Test",
          },
          {
            do: "x",
            action: "Action1",
            values: "Values1a, Values1b",
            rowNumber: "10",
          },
          {
            do: "x",
            action: "Action1",
            values: "a\\,b",
            rowNumber: "11",
          },
          {
            do: "x",
            action: "Action1",
            values: "a\\,b, value",
            rowNumber: "12",
          },
          {
            do: "x",
            action: "End Test",
          },
        ],
      },
    ],
  },
  scripts: {
    Sheet1: [
      {
        Id: "14",
        Script: "Script2",
        Steps: [
          { do: "x", action: "Start Script" },
          { do: "x", action: "Action1", rowNumber: "16" },
          { do: "x", action: "End Script" },
        ],
      },
    ],
    Sheet2: [
      {
        Id: "7",
        Script: "Script1",
        Steps: [
          { do: "x", action: "Start Script" },
          { do: "x", action: "Action1", rowNumber: "9" },
          { do: "x", action: "End Script" },
        ],
      },
    ],
  },
  tables: {
    Table1: {
      Id1: { Id: "Id1", Field1: "Field1-Id1", Field2: "Field2-Id1" },
      Id2: { Id: "Id2", Field1: "Field1-Id2", Field2: "Field2-Id2" },
    },
  },
};
