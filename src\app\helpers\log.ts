export default {
  titleLevel0: 0,
  titleLevel1: 1,
  titleLevel2: 2,
  titleLevel3: 3,
  titleLevel4: 4,

  consoleLog(...args: any[]): void {
    const textFormat = 'color: grey; font-size: 14px; font-weight: bold';
    console.log('='.repeat(50));
    args.forEach((value: any, index: number) => {
      if (args.length > 1 && index === 0) {
        console.log('%c' + value, textFormat);
      } else {
        console.log(value);
      }
    });
  },

  getStackTrace(n: number = 3): string {
    return new Error().stack
      .split('\n')
      .splice(3)
      .slice(0, n)
      .reduce((acc: string, value: string) => acc + '\n' + value);
  },
};
