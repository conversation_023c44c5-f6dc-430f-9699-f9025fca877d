import h from "@/app/helpers/all";

export default class ScheduleHandler {
  defaultSchedule = [
    {
      week: "Current + 1",
      day: "Monday",
      time: "08:00",
    },
    {
      week: "Current + 1",
      day: "Tuesday",
      time: "08:15",
    },
    {
      week: "Current + 2",
      day: "Monday",
      time: "08:00",
    },
    {
      week: "Current + 2",
      day: "Tuesday",
      time: "08:15",
    },
    {
      week: "Current + 3",
      day: "Monday",
      time: "08:00",
    },
    {
      week: "Current + 3",
      day: "Tuesday",
      time: "08:15",
    },
  ];

  parseSchedule(availableSchedule) {
    let schedule1;

    const parseScheduleStep1 = (schedule) => {
      schedule1 = schedule.reduce((previousValue, currentValue) => {
        const { week, day, time } = currentValue;
        const slot = { day, time };
        const scheduleEntry = {
          weekLabel: week,
          slots: [slot],
        };

        const foundScheduleEntry = previousValue.find(
          (scheduleEntryValue) => scheduleEntryValue.weekLabel === week
        );

        if (foundScheduleEntry) {
          foundScheduleEntry.slots.push(slot);
        } else {
          previousValue.push(scheduleEntry);
        }

        return previousValue;
      }, []);
    };

    const parseScheduleStep2 = () => {
      const newSchedule = schedule1.map(({ weekLabel, slots }) => {
        const weekSchedule = slots.reduce((previousValue, currentValue) => {
          const { day, time } = currentValue;
          const slot = {
            timeSlot: time,
            selected: true,
          };
          const dayOfWeek = h.getDayOfWeek(day);
          const slotEntry = {
            dayOfWeek,
            timeSlots: [slot],
          };

          const foundSlotEntry = previousValue.find(
            (slotEntryValue) => slotEntryValue.dayOfWeek === dayOfWeek
          );

          if (foundSlotEntry) {
            foundSlotEntry.timeSlots.push(slot);
          } else {
            previousValue.push(slotEntry);
          }

          return previousValue;
        }, []);

        const schedule = { weekLabel, weekSchedule };

        return schedule;
      });

      return newSchedule;
    };

    parseScheduleStep1(availableSchedule);
    return parseScheduleStep2();
  }

  setSchedule(user, schedule?) {
    const scheduleHandler = new ScheduleHandler();

    if (schedule) {
      Cypress.sdt.domain.user["availableSchedule"] = schedule;
    } else {
      if (!Cypress.sdt.domain.user["availableSchedule"]) {
        Cypress.sdt.domain.user["availableSchedule"] = scheduleHandler.defaultSchedule;
      }
    }

    Cypress.sdt.domain.user["availableSchedule"] = scheduleHandler.parseSchedule(
      Cypress.sdt.domain.user["availableSchedule"]
    );

    return cy
      .task("dbReadUser", Cypress.sdt.domain.user.username)
      .then((foundUser) => {
        Cypress.sdt.domain.user["availableSchedule"].forEach((week) => {
          const weekDates = h.getWeekStartAndEndDates(week.weekLabel);
          Cypress.sdt.apiHandler.createProviderSchedule(
            foundUser["_id"],
            weekDates.monday,
            weekDates.sunday,
            week.weekSchedule
          );
        });
      })
      .then(() => {
        if (!user.providersList) {
          user.providersList = [];
        }
        user.providersList.push(structuredClone(this));
      });
  }
}
