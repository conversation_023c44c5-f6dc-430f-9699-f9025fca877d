[{"_id": "68724c99f7ba75ab58acf386", "bhSeriesId": "68724c99f7ba75ab58acf37f", "physioSurveyId": null, "patientId": "68724c92f7ba75ab58acf2c6", "providerUserId": "68724c8ef7ba75ab58acf289", "encounterType": "bhScreenMINI", "title": "", "bypassSchedule": false, "scheduleDate": null, "zoom": {"startUrl": "", "joinUrl": "", "id": ""}, "timeSlotIds": [], "currentStatus": "Completed", "currentStatusDate": "2025-07-12T11:53:23.215Z", "currentStatusMsg": "", "appointmentStatus": "Pending", "office": {"officeDescription": "", "address": "", "address2": "", "city": "", "state": "", "zip": ""}, "pos": {"administrationOption": "practiceStaff", "telehealth": false, "phone": "", "selfAdminCode": "", "selfAdminUrl": "", "notifyInterviewer": false, "email": "", "patientInsuranceType": "medicaid", "currentStatus": "Completed", "currentStatusDate": "2025-07-12T11:53:24.083Z", "nViewSecurityTkn": "4lRDYxc4DFn5zb4NxKKC", "nViewInterviewerId": "68724c8af7ba75ab58acf25a", "interviewId": "211", "interviewCode": "MJCB94", "administrationAt": "2025-07-12T11:52:59.290Z", "interviewTestId": 30210, "nViewRedirectUrl": "http://localhost:4200/desktop/app/interview/?code=MJCB94", "nViewCompletedAt": "2025-07-12T11:53:24.083Z"}, "review": {"diagnosisIndex": [], "diagnosisCodes": [], "diagnosisDescriptions": []}, "statusHistory": [{"currentStatus": "Started", "currentStatusDate": "2025-07-12T11:52:59.290Z", "updatedById": "68724c8af7ba75ab58acf25a", "_id": "68724c9bf7ba75ab58acf3c1"}, {"currentStatus": "Started", "currentStatusDate": "2025-07-12T11:52:59.993Z", "updatedById": "68724c8af7ba75ab58acf25a", "_id": "68724c9bf7ba75ab58acf3d0"}, {"currentStatus": "Completed", "currentStatusDate": "2025-07-12T11:53:23.215Z", "_id": "68724cb3f7ba75ab58acf44c"}], "soapNotes": null, "interactionDuration": 0, "cptCodes": [], "createdById": "68724c8af7ba75ab58acf25a", "updatedById": "68724c8af7ba75ab58acf25a", "isActive": true, "locationId": "68724c88f7ba75ab58acf24c", "languagePreference": "en", "indicators": {"entries": []}, "nextSteps": [], "createdAt": "2025-07-12T11:52:57.326Z", "updatedAt": "2025-07-12T11:53:24.085Z", "__v": 0, "sds": {"isSDSEncounter": true, "currentStatus": "Completed", "currentStatusDate": "2025-07-12T11:53:24.021Z", "administrationOption": "practiceStaff", "administrationAt": "2025-07-12T11:52:59.993Z", "nViewSecurityTkn": "", "selfAdminCode": "", "selfAdminUrl": "", "interviewCode": "MJCB94", "interviewId": "8", "interviewTestId": 30211, "nViewRedirectUrl": "http://localhost:4200/desktop/app/interview/?code=MJCB94", "reportId": "30211", "reportInterviewTestId": "30211", "reportComplete": true, "nViewCompletedAt": "2025-07-12T11:53:24.021Z"}}, {"_id": "68724c99f7ba75ab58acf39c", "bhSeriesId": "68724c99f7ba75ab58acf37f", "appointmentId": "68724c99f7ba75ab58acf39a", "physioSurveyId": null, "patientId": "68724c92f7ba75ab58acf2c6", "providerUserId": "68724c8ef7ba75ab58acf289", "encounterType": "bhReviewScreen", "title": "", "bypassSchedule": true, "scheduleDate": null, "zoom": {"startUrl": "", "joinUrl": "", "id": ""}, "timeSlotIds": [], "currentStatus": "Completed", "currentStatusDate": "2025-07-12T11:53:47.745Z", "currentStatusMsg": "", "appointmentStatus": "Pending", "review": {"diagnosisIndex": [], "diagnosisCodes": [], "diagnosisDescriptions": [], "reportComplete": true, "reportCompleteAt": "2025-07-12T11:53:24.075Z", "reportId": "30210", "reportInterviewTestId": "30210", "dsmDisorders": true, "nextSteps": "miniInterview", "reviewDuration": 50}, "statusHistory": [{"currentStatus": "Scheduled", "currentStatusDate": "2025-07-12T11:52:57.383Z", "updatedById": "68724c8af7ba75ab58acf25a", "_id": "68724c99f7ba75ab58acf3a4"}, {"currentStatus": "Started", "currentStatusDate": "2025-07-12T11:53:23.207Z", "_id": "68724cb3f7ba75ab58acf448"}, {"currentStatus": "Completed", "currentStatusDate": "2025-07-12T11:53:47.745Z", "updatedById": "68724c8ef7ba75ab58acf289", "_id": "68724ccbf7ba75ab58acf5f9"}], "soapNotes": "Soap Notes", "interactionDuration": 0, "cptCodes": [], "createdById": "68724c8af7ba75ab58acf25a", "updatedById": "68724c8ef7ba75ab58acf289", "isActive": true, "locationId": "68724c88f7ba75ab58acf24c", "indicators": {"entries": []}, "nextSteps": [], "createdAt": "2025-07-12T11:52:57.367Z", "updatedAt": "2025-07-12T11:53:47.746Z", "__v": 0, "office": {}, "dsmDisorders": true}, {"_id": "68724ccbf7ba75ab58acf618", "bhSeriesId": "68724c99f7ba75ab58acf37f", "appointmentId": "68724ccbf7ba75ab58acf616", "physioSurveyId": null, "patientId": "68724c92f7ba75ab58acf2c6", "providerUserId": "68724c8ef7ba75ab58acf289", "encounterType": "bhReviewInterview", "title": "", "bypassSchedule": false, "scheduleDate": null, "zoom": {"startUrl": "", "joinUrl": "", "id": ""}, "timeSlotIds": [], "currentStatus": "Completed", "currentStatusDate": "2025-07-12T12:30:57.457Z", "currentStatusMsg": "", "appointmentStatus": "Pending", "review": {"diagnosisIndex": [{"seq": 0, "index": 0}], "diagnosisCodes": ["Z03.89"], "diagnosisDescriptions": ["Z03.89 Encounter for observation for other suspected diseases and conditions ruled out"], "reportComplete": true, "reportCompleteAt": "2025-07-12T11:55:29.734Z", "reportId": "30212", "reportInterviewTestId": "30212", "doesNotMeetCriteria": true}, "statusHistory": [{"currentStatus": "NotStarted", "currentStatusDate": "2025-07-12T11:53:47.811Z", "updatedById": "68724c8ef7ba75ab58acf289", "_id": "68724ccbf7ba75ab58acf61f"}, {"currentStatus": "Started", "currentStatusDate": "2025-07-12T11:55:29.661Z", "_id": "68724d31f7ba75ab58acfe60"}, {"currentStatus": "Completed", "currentStatusDate": "2025-07-12T12:30:57.457Z", "updatedById": "68724c8ef7ba75ab58acf289", "_id": "68725581f7ba75ab58ad05f0"}], "soapNotes": null, "interactionDuration": 0, "cptCodes": [], "createdById": "68724c8ef7ba75ab58acf289", "updatedById": "68724c8ef7ba75ab58acf289", "isActive": true, "locationId": "68724c88f7ba75ab58acf24c", "indicators": {"templateId": "5fbc0241fcd7ffed7c251002", "createdAt": "2025-07-12T12:30:56.154Z", "entries": [{"entriesNotFoundCount": 1, "entryNum": 1, "entriesTotal": 1, "entriesFound": [30], "notExploredFound": []}, {"entriesNotFoundCount": 1, "entryNum": 2, "entriesTotal": 1, "entriesFound": [9], "notExploredFound": []}]}, "nextSteps": [], "createdAt": "2025-07-12T11:53:47.799Z", "updatedAt": "2025-07-12T12:30:57.458Z", "__v": 0, "office": {}}, {"_id": "68724cd8f7ba75ab58acf6f7", "bhSeriesId": "68724c99f7ba75ab58acf37f", "physioSurveyId": null, "patientId": "68724c92f7ba75ab58acf2c6", "providerUserId": "68724c8ef7ba75ab58acf289", "encounterType": "bhInterview", "title": "", "bypassSchedule": false, "scheduleDate": null, "zoom": {"startUrl": "", "joinUrl": "", "id": ""}, "timeSlotIds": [], "currentStatus": "Completed", "currentStatusDate": "2025-07-12T11:55:29.670Z", "currentStatusMsg": "", "appointmentStatus": "Pending", "office": {"officeDescription": "", "address": "", "address2": "", "city": "", "state": "", "zip": ""}, "pos": {"administrationOption": "practiceStaff", "telehealth": false, "phone": "", "selfAdminCode": "", "selfAdminUrl": "", "notifyInterviewer": false, "email": "", "patientInsuranceType": "medicaid", "currentStatus": "Completed", "currentStatusDate": "2025-07-12T11:55:29.742Z", "nViewSecurityTkn": "mmx3GKtlSEUdhTyUotHW", "nViewInterviewerId": "68724c8af7ba75ab58acf25a", "interviewId": "292", "interviewCode": "DQXTNX", "administrationAt": "2025-07-12T11:54:02.371Z", "interviewTestId": 30212, "nViewRedirectUrl": "http://localhost:4200/desktop/app/interview/?code=DQXTNX", "nViewCompletedAt": "2025-07-12T11:55:29.742Z"}, "review": {"diagnosisIndex": [], "diagnosisCodes": [], "diagnosisDescriptions": []}, "statusHistory": [{"currentStatus": "Started", "currentStatusDate": "2025-07-12T11:54:02.371Z", "updatedById": "68724c8af7ba75ab58acf25a", "_id": "68724cdaf7ba75ab58acf70f"}, {"currentStatus": "Completed", "currentStatusDate": "2025-07-12T11:55:29.670Z", "_id": "68724d31f7ba75ab58acfe64"}], "soapNotes": null, "interactionDuration": 0, "cptCodes": [], "createdById": "68724c8af7ba75ab58acf25a", "updatedById": "68724c8af7ba75ab58acf25a", "isActive": true, "locationId": "68724c88f7ba75ab58acf24c", "languagePreference": "en", "indicators": {"entries": []}, "nextStepsModuleName": "miniInterview", "nextSteps": [], "createdAt": "2025-07-12T11:54:00.994Z", "updatedAt": "2025-07-12T11:55:29.744Z", "__v": 0}]