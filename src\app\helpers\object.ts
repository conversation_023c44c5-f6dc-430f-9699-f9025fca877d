import { NormalizedString } from "@/app/helpers/string";
import commonHelper from "@/app/helpers/common";

const objectHelper = {
  mapToNestedObject(map) {
    const obj = {};
    map.forEach((value, key) => {
      if (value instanceof Map) {
        // If the value is another Map, recursively convert it to an object
        obj[key] = objectHelper.mapToNestedObject(value);
      } else if (Array.isArray(value)) {
        // If the value is an array, recursively convert its elements
        obj[key] = value.map((item) => {
          if (item instanceof Map) {
            return objectHelper.mapToNestedObject(item);
          } else {
            return item;
          }
        });
      } else if (value && typeof value === "object") {
        // If the value is an object, recursively convert it
        obj[key] = objectHelper.mapToNestedObject(
          new Map(Object.entries(value))
        );
      } else {
        obj[key] = value;
      }
    });
    return obj;
  },

  getObjectFieldValueWithNormalizedKey(object, keyPath) {
    if (!object || !keyPath) {
      return object;
    }
    return keyPath.split(".").reduce((obj, keyPart) => {
      const normalizedKeyPart = new NormalizedString(keyPart).normalizedValue;
      if (!obj || !commonHelper.isObject(obj)) obj = {};
      const foundKey = Object.keys(obj).find((key) => {
        const normalizedKey = new NormalizedString(key).normalizedValue;
        return normalizedKey === normalizedKeyPart;
      });
      if (foundKey) return obj[foundKey];
      return null;
    }, object);
  },

  checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
    obj1,
    obj2,
    key1?,
    key2?
  ) {
    if (key1 && !key2) {
      key2 = key1;
    }
    const value1 = this.getObjectFieldValueWithNormalizedKey(obj1, key1);
    const value2 = this.getObjectFieldValueWithNormalizedKey(obj2, key2);
    return JSON.stringify(value1) === JSON.stringify(value2);
  },

  //Use the Levenshtein Distance algorithm for fuzzy string matching,
  // which calculates the minimum number of single-character edits required
  // to change one string into another
  getObjectSimilarKey(object, keyToFind, similarityThreshold = 0.8) {
    const keyToFindReducedValue = new NormalizedString(keyToFind).reducedValue;

    // Calculate Levenshtein distance between two strings
    const levenshteinDistance = (str1: string, str2: string): number => {
      const matrix: number[][] = [];

      for (let i = 0; i <= str1.length; i++) {
        matrix[i] = [i];
      }

      for (let j = 0; j <= str2.length; j++) {
        matrix[0][j] = j;
      }

      for (let i = 1; i <= str1.length; i++) {
        for (let j = 1; j <= str2.length; j++) {
          const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
          matrix[i][j] = Math.min(
            matrix[i - 1][j] + 1, // deletion
            matrix[i][j - 1] + 1, // insertion
            matrix[i - 1][j - 1] + cost // substitution
          );
        }
      }

      return matrix[str1.length][str2.length];
    };

    // Calculate similarity ratio between 0 and 1
    const calculateSimilarity = (str1: string, str2: string): number => {
      const maxLength = Math.max(str1.length, str2.length);
      const distance = levenshteinDistance(str1, str2);
      return (maxLength - distance) / maxLength;
    };

    // Find the most similar key
    let mostSimilarKey: string | undefined;
    let highestSimilarity = 0;

    Object.getOwnPropertyNames(object).forEach((key) => {
      const reducedKey = new NormalizedString(key).reducedValue;
      const similarity = calculateSimilarity(reducedKey, keyToFindReducedValue);

      if (similarity > highestSimilarity && similarity >= similarityThreshold) {
        highestSimilarity = similarity;
        mostSimilarKey = key;
      }
    });

    return mostSimilarKey;
  },

  parseObjectWithTemplate(input: any, template: any): any {
    if (typeof input !== "object" || typeof template !== "object") {
      return {};
    }

    if (input === null || Object.keys(input).length === 0) return template;
    if (template === null || Object.keys(template).length === 0) return input;

    const result: any = {};

    for (const key in template) {
      const normalizedKey = key.replace(/\s+/g, "").toLowerCase();
      for (const inputKey in input) {
        const normalizedInputKey = inputKey.replace(/\s+/g, "").toLowerCase();
        if (normalizedKey === normalizedInputKey) {
          if (Array.isArray(template[key])) {
            result[key] = Array.isArray(input[inputKey])
              ? input[inputKey]
              : [input[inputKey]];
          } else if (
            typeof template[key] === "object" &&
            typeof input[inputKey] === "object"
          ) {
            result[key] = this.parseObjectWithTemplate(
              input[inputKey],
              template[key]
            );
          } else {
            result[key] = input[inputKey];
          }
          break;
        }
      }
    }

    return result;
  },

  mergeObjects(objectA, objectB) {
    const mergedObject = {};

    for (const property in objectA) {
      if (commonHelper.isObject(objectA[property]) && commonHelper.isObject(objectB?.[property])) {
        mergedObject[property] = this.mergeObjects(
          objectA[property],
          objectB[property]
        );
      } else {
        mergedObject[property] = objectA[property];
      }
    }

    for (const property in objectB) {
      if (
        (commonHelper.isObject(objectB[property]) || commonHelper.isArray(objectB[property])) &&
        typeof mergedObject[property] === "undefined"
      ) {
        mergedObject[property] = objectB[property];
      } else if (typeof objectB[property] !== "object") {
        mergedObject[property] = objectB[property];
      }
    }

    return mergedObject;
  },

  hasCircularReferences(obj) {
    try {
      JSON.stringify(obj);
      return false; // No circular references found
    } catch (error) {
      if (error.message.includes('circular')) {
        console.log('Circular reference detected');
        return true;
      }
      // If it's some other error, rethrow it
      throw error;
    }
  },
};

export default objectHelper;
