export default {
  quotedStrings: [],
  getNormalizedString(value: string, rules?: string[] | undefined) {
    return new NormalizedString(value, rules);
  },

  getNormalizedValue(value: string, rules?: string[] | undefined) {
    return new NormalizedString(value, rules).normalizedValue;
  },

  compareNormalizedStrings(value1: string, value2: string): boolean {
    return new NormalizedString(value1).isEqual(value2);
  },

  includesNormalizedString(value1: string, value2: string): boolean {
    return new NormalizedString(value1).includes(value2);
  },

  normalizeString(
    stringToNormalize: string,
    regex = new RegExp("[^A-Za-z0-9]", "g")
  ) {
    return stringToNormalize.replace(regex, "").toLowerCase().trim();
  },

  getNormalizedValueInList(value: string, list: string[]): string | undefined {
    const normalizedValue = new NormalizedString(value);
    return normalizedValue.getNormalizedValueInList(list);
  },

  toCamelCase(str: string): string {
    if (!str) return "";
    str = str.trim();
    str = str.replace(/\s+/g, "_");
    return str
      .split("_")
      .map((word, index) =>
        index === 0
          ? word.charAt(0).toLowerCase() + word.slice(1)
          : word.charAt(0).toUpperCase() + word.slice(1)
      )
      .join("");
  },

  fromCamelCase(str: string): string {
    if (!str) return "";
    str = str.trim();
    str = str.replace(/([A-Z])/g, " $1");
    return str.split(" ").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
  },

  replaceEscapedCharacters(text) {
    return text
      ?.replace(/\\,/g, "§")
      .replace(/\\:/g, "§§")
      .replace(/\\=/g, "§§§")
      .replace(/\\>/g, "§§§§")
      .replace(/\\\{/g, "§§§§§")
      .replace(/\\\}/g, "§§§§§§")
      .replace(/\\\[/g, "§§§§§§§")
      .replace(/\\\]/g, "§§§§§§§§")
      .replace(/\\\(/g, "§§§§§§§§§")
      .replace(/\\\)/g, "§§§§§§§§§§")
      .replace(/\\«/g, "§§§§§§§§§§§")
      .replace(/\\»/g, "§§§§§§§§§§§§")
      .replace(/\\"/g, "§§§§§§§§§§§§§");
  },

  restoreEscapedCharacters(text) {
    return text
      ?.replace(/§§§§§§§§§§§§§/g, '\\"')
      ?.replace(/§§§§§§§§§§§§/g, "\\»")
      .replace(/§§§§§§§§§§§/g, "\\«")
      .replace(/§§§§§§§§§§/g, "\\)")
      .replace(/§§§§§§§§§/g, "\\(")
      .replace(/§§§§§§§§/g, "\\]")
      .replace(/§§§§§§§/g, "\\[")
      .replace(/§§§§§§/g, "\\}")
      .replace(/§§§§§/g, "\\{")
      .replace(/§§§§/g, "\\>")
      .replace(/§§§/g, "\\=")
      .replace(/§§/g, "\\:")
      .replace(/§/g, "\\,");
  },

  parseEscapedCharacters(text) {
    return text
      ?.replace(/§§§§§§§§§§§§§/g, '"')
      ?.replace(/§§§§§§§§§§§§/g, "»")
      .replace(/§§§§§§§§§§§/g, "«")
      .replace(/§§§§§§§§§§/g, ")")
      .replace(/§§§§§§§§§/g, "(")
      .replace(/§§§§§§§§/g, "]")
      .replace(/§§§§§§§/g, "[")
      .replace(/§§§§§§/g, "}")
      .replace(/§§§§§/g, "{")
      .replace(/§§§§/g, ">")
      .replace(/§§§/g, "=")
      .replace(/§§/g, ":")
      .replace(/§/g, ",");
  },

  replaceQuotedStrings(value) {
    if (value && typeof value === "string")
      value = value?.replace(/"([^"]*)"/g, (match) => {
        this.quotedStrings.push(match);
        return `¶${this.quotedStrings.length - 1}¶`;
      });
    return value;
  },

  restoreQuotedStrings(value) {
    if (value && typeof value === "string")
      value = value?.replace(/¶(\d+)¶/g, (_, index) => {
        return this.quotedStrings[parseInt(index)];
      });
    return value;
  },

  parseQuotedStrings(value) {
    if (value && typeof value === "string")
      value = value?.replace(/¶(\d+)¶/g, (_, index) => {
        return this.quotedStrings[parseInt(index)].replace(/^"|"$/g, "");
      });
    return value;
  },

  replaceEscapedCharactersAndQuotedStrings(text) {
    let result = this.replaceEscapedCharacters(text);
    result = this.replaceQuotedStrings(result);
    return result;
  },

  restoreQuotedStringsAndEscapedCharacters(text) {
    let result = this.restoreQuotedStrings(text);
    result = this.restoreEscapedCharacters(result);
    return result;
  },

  parseQuotedStringsAndEscapedCharacters(text) {
    let result = this.parseQuotedStrings(text);
    result = this.parseEscapedCharacters(result);
    return result;
  },
};

export class NormalizedString {
  originalValue: string;
  normalizedValue = "";
  reducedValue = "";

  constructor(value: string, rules?: string[] | undefined) {
    this.originalValue = value;
    this.normalize(rules);
    this.reduce();
  }

  normalize(rules?: string[] | undefined): this {
    this.normalizedValue = this.originalValue;
    const ruleSet = [
      "noHtmlBreaks",
      "noBlanks",
      "onlyDigitsAndCharacters",
      "upperCase",
      "lowerCase",
    ];

    if (!rules) {
      rules = ruleSet;
    }

    rules.forEach((rule) => {
      if (rule.toLowerCase().trim() === "nohtmlbreaks") {
        this.normalizedValue = this.normalizedValue?.replace(/<br\s*\/>/g, "");
      }
      if (rule.toLowerCase().trim() === "noblanks") {
        this.normalizedValue = this.normalizedValue
          ?.replace(/<br\s*\/>/g, "")
          .replace(/\s*/g, "");
      }
      if (rule.toLowerCase().trim() === "onlydigitsandcharacters") {
        this.normalizedValue = this.normalizedValue?.replace(/[^0-9a-z]/gi, "");
      }
      if (rule.toLowerCase().trim() === "uppercase") {
        this.normalizedValue = this.normalizedValue?.toUpperCase();
      }
      if (rule.toLowerCase().trim() === "lowercase") {
        this.normalizedValue = this.normalizedValue?.toLowerCase().trim();
      }
    });

    return this;
  }

  reduce(): this {
    this.reducedValue = this.normalizedValue?.split("").sort().join("");
    return this;
  }

  isEqual(valueToCheck: string): boolean {
    const normalizedStringToCheck = new NormalizedString(valueToCheck);
    return this.normalizedValue === normalizedStringToCheck.normalizedValue;
  }

  isSimilar(valueToCheck: string): boolean {
    const normalizedStringToCheck = new NormalizedString(valueToCheck);
    return this.reducedValue === normalizedStringToCheck.reducedValue;
  }

  includes(valueToCheck: string): boolean {
    const normalizedStringToCheck = new NormalizedString(valueToCheck);
    return this.normalizedValue?.includes(
      normalizedStringToCheck.normalizedValue
    );
  }

  getNormalizedValueInList(list: Array<string>): string | undefined {
    return list
      .map((value) => new NormalizedString(value))
      .find((normalizedValue) => {
        return this.normalizedValue === normalizedValue.normalizedValue;
      })?.originalValue;
  }

  remove(valueToExclude: string): this {
    const normalizedValueToExclude = new NormalizedString(valueToExclude);
    this.normalizedValue = this.normalizedValue.replace(
      normalizedValueToExclude.normalizedValue,
      ""
    );
    this.reducedValue = this.normalizedValue.split("").sort().join("");
    return this;
  }

  circularSafeJSONStringify(obj) {
    const seen = new WeakSet();

    function replacer(_key, value) {
      if (typeof value === "object" && value !== null) {
        if (seen.has(value)) {
          return "[Circular Reference]";
        }
        seen.add(value);
      }
      return value;
    }

    return JSON.stringify(obj, replacer);
  }
}
