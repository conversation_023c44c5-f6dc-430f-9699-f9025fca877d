import h from "@/app/helpers/all";

export default {
  getMsInCurrentHour(): number {
    const currentTime = new Date().getTime();
    const startOfHour = new Date().setHours(0, 0, 0, 0);
    const elapsedHoursInMs = currentTime - startOfHour;
    const elapsedHours = Math.floor(elapsedHoursInMs / (3600 * 1000));
    const msInCurrentHour = elapsedHoursInMs - elapsedHours * 3600 * 1000;
    return msInCurrentHour;
  },

  convertDateStringFormat(date: string): string {
    const d = new Date(date);
    return (
      d.toLocaleDateString() +
      " " +
      d.toLocaleTimeString([], { hour: "numeric", minute: "numeric" })
    );
  },

  previousMondayDate(date: string): Date {
    const curr = new Date(date);
    return new Date(curr.setDate(curr.getDate() - curr.getDay() + 1));
  },

  nextSundayDate(date: string): Date {
    const curr = new Date(date);
    return new Date(curr.setDate(curr.getDate() - curr.getDay() + 7));
  },

  getMondayDateAfterWeeks(weeks: number): Date {
    const curr = new Date();
    const day = curr.getDay();
    const diff = 7 - day; // Calculate the difference to the next Monday
    const weeksToMonday = weeks * 7; // Convert weeks to days

    // Calculate the date of the Monday after the given number of weeks
    const targetMonday = new Date(
      curr.setDate(curr.getDate() + weeksToMonday + diff)
    );
    return targetMonday;
  },

  getWeekStartAndEndDates(weekLabel = "Current + 1"): {
    monday: string;
    sunday: string;
  } {
    const weekOrderNumber = +weekLabel.match(/\d+/)![0];
    const d = new Date();

    d.setUTCDate(
      d.getUTCDate() + 7 * (weekOrderNumber - 1) + ((7 - d.getUTCDay()) % 7) + 1
    );
    const mondayDate = d.toISOString().substring(0, 10);

    d.setUTCDate(d.getUTCDate() + 6);
    const sundayDate = d.toISOString().substring(0, 10);

    return { monday: mondayDate, sunday: sundayDate };
  },

  getMondayDate(weekLabel: string): string {
    const weekOrderNumber = +weekLabel.match(/\d+/)![0];
    const d = new Date();

    d.setUTCDate(
      d.getUTCDate() + 7 * (weekOrderNumber - 1) + ((7 - d.getUTCDay()) % 7) + 1
    );
    const mondayDate = d.toISOString().substring(0, 10);
    return mondayDate;
  },

  getUSDayDate(weekLabel: string, weekday: string): string {
    const weekOrderNumber = +weekLabel.match(/\d+/)![0];

    const weekDayOffset =
      ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"].indexOf(
        weekday
      ) + 1;

    const d = new Date();
    d.setUTCDate(
      d.getUTCDate() +
        7 * (weekOrderNumber - 1) +
        ((7 - d.getUTCDay()) % 7) +
        weekDayOffset
    );

    return d.toLocaleDateString("en-US");
  },

  getNextDayDate(): string {
    const d = new Date();
    d.setDate(d.getDate() + 1);
    return `${d.getMonth() + 1}/${d.getDate()}/${d.getFullYear()}`;
  },

  getIsoDateFromLocaleDate(localeDate: string): string {
    const date = new Date(localeDate);
    return date.toISOString().substring(0, 10);
  },

  getMongoDateFromUsaDate(date: string | undefined): string | null {
    if (!date) {
      return null;
    }
    const dateArray = date.split("/");
    let month = dateArray[0];
    if (month.length < 2) {
      month = "0" + month;
    }
    let day = dateArray[1];
    if (day.length < 2) {
      day = "0" + day;
    }
    const year = dateArray[2];
    return `${year}-${month}-${day}`;
  },

  getUsaLocaleDateFromIsoDate(isoDate: string): string {
    return new Date(Date.parse(isoDate)).toLocaleDateString("en-US");
  },

  getCST(date: string | undefined): string | null {
    if (!date) {
      return null;
    }

    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

    const d = new Date(date);

    // CST is UTC -0600 so subtract 6 hours and use UTC values
    d.setUTCHours(d.getUTCHours() - 6);

    const dayOfWeek = daysOfWeek[d.getUTCDay()];
    const month = ("0" + (d.getUTCMonth() + 1)).slice(-2);
    const day = ("0" + d.getUTCDate()).slice(-2);
    const year = d.getUTCFullYear();
    const hours = ("0" + (d.getUTCHours() % 12 || 12)).slice(-2);
    const minutes = ("0" + d.getUTCMinutes()).slice(-2);
    const amOrPm = d.getUTCHours() < 12 ? "am" : "pm";

    return `${dayOfWeek}, ${month}/${day}/${year} ${hours}:${minutes} ${amOrPm} CST`;
  },

  getLocalDateAndTime(separators = true) {
    const dt = new Date();
    const currentDayOfMonth = dt.getDate();
    const currentMonth = dt.getMonth() + 1;
    const currentYear = dt.getFullYear();
    const currentHours = dt.getHours();
    const currentMinutes = dt.getMinutes();
    const currentSeconds = dt.getSeconds();

    const currentDayOfMonthString =
      currentDayOfMonth < 10 ? "0" + currentDayOfMonth : currentDayOfMonth;
    const currentMonthString =
      currentMonth < 10 ? "0" + currentMonth : currentMonth;
    const currentHoursString =
      currentHours < 10 ? "0" + currentHours : currentHours;
    const currentMinutesString =
      currentMinutes < 10 ? "0" + currentMinutes : currentMinutes;
    const currentSecondsString =
      currentSeconds < 10 ? "0" + currentSeconds : currentSeconds;

    let dateSeparator = "-";
    let timeSeparator = ".";

    if (!separators) {
      dateSeparator = "";
      timeSeparator = "";
    }

    const date = `${currentYear}${dateSeparator}${currentMonthString}${dateSeparator}${currentDayOfMonthString}`;
    const time = `${currentHoursString}${timeSeparator}${currentMinutesString}${timeSeparator}${currentSecondsString}`;

    return `${date}T${time}`;
  },

  convertUtcDateToIsoFormat(date, format) {
    format = format.toLowerCase().trim();

    const dateParts = [
      ["y", format.indexOf("y")],
      ["m", format.indexOf("m")],
      ["d", format.indexOf("d")],
    ]
      .sort(([_partId1, index1], [_partId2, index2]) => index1 - index2)
      .map(([partId, _index]) => partId);

    let year, month, day;
    date.split(/[^\d]/g).map((part, index) => {
      switch (dateParts[index]) {
        case "y":
          year = part;
          break;
        case "m":
          month = part - 1;
          break;
        case "d":
          day = part;
          break;
      }
    });

    const dateObj = new Date(Date.UTC(year, month, day));

    return dateObj.toISOString().substring(0, 10);
  },

  getCalendarEntry(slot?: { day: number | string; time: string }): string {
    if (!slot) {
      slot = { day: 0, time: "08:00" };
    }

    if (typeof slot.day !== "number") {
      slot.day = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
      ].findIndex((elem: string) => {
        const normalizedElem = h.getNormalizedValue(elem);
        return normalizedElem === slot.day;
      }) as number;
    }

    const hour = +slot.time.substring(0, 2);
    const quarter = +slot.time.substring(3) / 15;
    const daySlot = (hour - 8) * 4 + quarter;
    const columnOffset = 2 + slot.day;
    const rowOffset = 1 + daySlot;

    return `.schedule .column:nth-of-type(${columnOffset}) .calendar-day-entry:nth-of-type(${rowOffset})`;
  },

  getDayOfWeek(day: string): number {
    const daysOfWeek = [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ];
    return daysOfWeek.findIndex((element) => element === day);
  },

  getRegexFromPhoneNumber(phoneNumber: string): RegExp {
    const paddedPhoneNumber = phoneNumber
      .replace(/\D/g, "")
      .replace(/^0+/g, "")
      .padEnd(10, "_");

    return new RegExp(
      `\\(${paddedPhoneNumber.substring(0, 3)}\\) ${paddedPhoneNumber.substring(
        3,
        6
      )}-${paddedPhoneNumber.substring(6)}`
    );
  },
};
