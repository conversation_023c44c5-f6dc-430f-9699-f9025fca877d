import User from "../domain/user";

export default {
  "Create User": function (userData) {
    userData =
      userData ||
      Cypress.sdt.current.step.simpleValues[0] ||
      Cypress.sdt.domain.getCurrentEntity();
    Cypress.sdt.domain.data.user = new User(userData);
    Cypress.sdt.domain.data.user.create();
  },

  "Create User And Login": function (userData) {
    userData =
      userData ||
      Cypress.sdt.current.step.simpleValues[0] ||
      Cypress.sdt.domain.getCurrentEntity();
    cy.wrap(Cypress.sdt.actions["Create User"](userData)).then(() => {
      Cypress.sdt.domain.data.user.login();
    });
  },

  "Get App Config": function () {
    Cypress.sdt.apiHandler.getAppConfig();
  },

  Login: function (userData) {
    const data =
      userData ||
      Cypress.sdt.current.step.simpleValues[0] ||
      Cypress.sdt.domain.getCurrentEntity();
    Cypress.sdt.apiHandler.logout().then(() => {
      Cypress.sdt.apiHandler.login(data.Username, data.Password);
    });
  },

  "Login Admin": function () {
    const { username, password } = Cypress.sdt.config.admin;
    Cypress.sdt.apiHandler.logout().then(() => {
      Cypress.sdt.apiHandler.login(username, password);
    });
  },

  Logout: function () {
    Cypress.sdt.apiHandler.logout();
  },
};
