[{"_id": "6880f79fbdcf844a44b90a39", "profileName": "Past Due Admin", "menuItems": [{"menuTitle": "Past Due Data Extract", "menuRoute": "app/past-due/extract", "roles": ["PAST_DUE_EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "PAST_DUE_EXTRACT_CSV"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a3a", "profileName": "ASP User Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a3b", "profileName": "ASP_ADMIN", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a3c", "profileName": "ASP_CSR", "menuItems": [{"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "ADD_ASP_CUSTOMER"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a3d", "profileName": "ASP Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["EDIT_TEMPLATE"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a3e", "profileName": "CSR", "menuItems": [{"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a3f", "profileName": "Electric Operations", "menuItems": [{"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a40", "profileName": "Revenue Operations", "menuItems": [{"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a41", "profileName": "Customer Billing", "menuItems": [{"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/cbmm/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a42", "profileName": "Customer Care", "menuItems": [{"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a43", "profileName": "Business Center", "menuItems": [{"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a44", "profileName": "Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "menuItems": [{"menuTitle": "Profile", "menuRoute": "profiles/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "User Admin", "menuRoute": "user-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Agencies", "menuRoute": "app/agency/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Users Upload", "menuRoute": "app/user-upload/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}], "expand": true, "elementRef": null}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "Search", "menuRoute": "app/csr/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "CSV Extract", "menuRoute": "app/reports/csv-extract", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Summary report", "menuRoute": "app/reports/shutoff-hold-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a45", "profileName": "Super Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "menuItems": [{"menuTitle": "Profile", "menuRoute": "profiles/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "User Admin", "menuRoute": "user-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Agencies", "menuRoute": "app/agency/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Users Upload", "menuRoute": "app/user-upload/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Usage Report", "menuRoute": "usage-report", "expand": false, "elementRef": null, "menuItems": []}], "expand": true, "elementRef": null}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Billing Search", "menuRoute": "app/cbmm/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Direct Payment Office Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Demand Response Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSV Extract", "menuRoute": "app/reports/csv-extract", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Summary report", "menuRoute": "app/reports/shutoff-hold-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a46", "profileName": "<PERSON><PERSON>", "menuItems": [{"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a47", "profileName": "Direct Payment Office", "menuItems": [{"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}, {"_id": "6880f79fbdcf844a44b90a48", "profileName": "Demand Response", "menuItems": [{"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0}]