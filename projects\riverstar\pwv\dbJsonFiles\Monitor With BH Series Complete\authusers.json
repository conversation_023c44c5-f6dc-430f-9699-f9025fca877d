[{"_id": "687247b3f7ba75ab58acf222", "firstName": "Partner", "lastName": "User", "email": "<EMAIL>", "username": "partner", "profile": "5efddf3153662557ac588b16", "password": "$2a$13$0lZjwB86LedVr2SkzzN/sefL6xT2PnrS/LjwO0QBwfpE4cPnGaefm", "passwordHashVersion": 2, "salt": "$2a$13$0lZjwB86LedVr2SkzzN/se", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-07-12T11:32:03.315Z", "updatedAt": "2025-07-12T11:32:03.315Z", "__v": 0}, {"_id": "687247b3f7ba75ab58acf223", "firstName": "NPI Practice Holder", "lastName": "User", "email": "<EMAIL>", "username": "practice", "profile": "5f049c66b67089862c39b03b", "password": "$2a$13$2d0YGcG/eXAd6TZXnRe9s.ZhYykxWAEThdmnbAQpQEoLZ6dq5pdLC", "passwordHashVersion": 2, "salt": "$2a$13$2d0YGcG/eXAd6TZXnRe9s.", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-07-12T11:32:03.315Z", "updatedAt": "2025-07-12T11:32:03.315Z", "__v": 0}, {"_id": "687247b3f7ba75ab58acf220", "firstName": "Practitioner", "lastName": "User", "email": "<EMAIL>", "username": "practitioner", "profile": "5efdce1bcf29e3600494f922", "password": "$2a$13$9YzI2iSFdJv3VwCj8fuG3eNVwwA9DT7se1.P5XZu3km3jO0tYrFQG", "passwordHashVersion": 2, "salt": "$2a$13$9YzI2iSFdJv3VwCj8fuG3e", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-07-12T11:32:03.315Z", "updatedAt": "2025-07-12T11:32:03.315Z", "__v": 0}, {"_id": "687247b3f7ba75ab58acf21f", "firstName": "Admin", "lastName": "Admin", "email": "<EMAIL>", "username": "admin", "profile": "6eb01161af41f774300ead35", "password": "$2a$13$LuC2gUsi6iEkK/7x.OBdde4VwetbSdvBvv3aVdNezbzs3cZFeQ1Si", "passwordHashVersion": 2, "salt": "$2a$13$LuC2gUsi6iEkK/7x.OBdde", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-07-12T11:32:03.315Z", "updatedAt": "2025-07-12T11:52:50.318Z", "__v": 0, "passwordBadCount": 0, "passwordSettings": null, "lastLogin": "2025-07-12T11:52:50.317Z"}, {"_id": "687247b3f7ba75ab58acf221", "firstName": "Clinician", "lastName": "User", "email": "<EMAIL>", "username": "clinician", "profile": "5efddf3153662557ac588b16", "password": "$2a$13$7PFk8dsn6XAB4tdrP0l6ROIA1LM6zIxha02SNvbD5yTXssI7YwHNS", "passwordHashVersion": 2, "salt": "$2a$13$7PFk8dsn6XAB4tdrP0l6RO", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-07-12T11:32:03.315Z", "updatedAt": "2025-07-12T11:32:03.315Z", "__v": 0}, {"_id": "687247b3f7ba75ab58acf225", "firstName": "RiverStar", "lastName": "User", "email": "<EMAIL>", "username": "riverStar", "profile": "5f049c7eb67089862c39b03c", "password": "$2a$13$jWMK2HqYq.kyYdgIStCEYO6MXvXDHPkghoLPnkR1tSCuoK1NZELlq", "passwordHashVersion": 2, "salt": "$2a$13$jWMK2HqYq.kyYdgIStCEYO", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-07-12T11:32:03.315Z", "updatedAt": "2025-07-12T11:32:03.315Z", "__v": 0}, {"_id": "687247b3f7ba75ab58acf224", "firstName": "Office Manager", "lastName": "User", "email": "<EMAIL>", "username": "office", "profile": "5f4fec51da389c0a1134a99f", "password": "$2a$13$C9UlRTBJbEw0umggsDpdyuQ9oQzPBuXUoebyY08vxPzqsMjYIT9rS", "passwordHashVersion": 2, "salt": "$2a$13$C9UlRTBJbEw0umggsDpdyu", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-07-12T11:32:03.315Z", "updatedAt": "2025-07-12T11:32:03.315Z", "__v": 0}, {"_id": "687247b3f7ba75ab58acf226", "firstName": "CCH", "lastName": "User", "email": "<EMAIL>", "username": "cch", "profile": "5f0791ee09802e4f20a017c9", "password": "$2a$13$fcfMGO4ChuSQZ2SkwxxWSOWL0eUTUlYQ96hn/n4/GSwwZO2WiTN1.", "passwordHashVersion": 2, "salt": "$2a$13$fcfMGO4ChuSQZ2SkwxxWSO", "roles": [], "tokenUser": true, "isActive": true, "parentApp": "CCH", "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-07-12T11:32:03.315Z", "updatedAt": "2025-07-12T11:32:03.315Z", "__v": 0}, {"_id": "68724c8af7ba75ab58acf25a", "firstName": "e2e-Clinician1-FN", "middleName": "", "lastName": "e2e-Clinician1-LN", "email": "<EMAIL>", "username": "e2e-Clinician1", "profile": "5efddf3153662557ac588b16", "createdById": "687247b3f7ba75ab58acf21f", "adminUpdatedAt": "2025-07-12T11:52:42.041Z", "updatedById": "687247b3f7ba75ab58acf21f", "phone": "**********", "notes": "", "roles": [], "tokenUser": false, "isActive": true, "activeUpdatedAt": "2025-07-12T11:52:41.196Z", "acceptedTermsAndConditions": true, "isSelfService": false, "ssoLookup": "", "passwordHistory": [], "createdAt": "2025-07-12T11:52:42.043Z", "updatedAt": "2025-07-12T11:53:53.987Z", "__v": 0, "passwordBadCount": 0, "password": "$2a$13$I/NAogEmalk8n/Th9d7XV.CiTworeTT7yNKMqxU/QEAXT1Px1B9.e", "passwordHashVersion": 2, "passwordLockoutUntil": null, "salt": "$2a$13$I/NAogEmalk8n/Th9d7XV.", "passwordSettings": null, "lastLogin": "2025-07-12T11:53:53.986Z", "pwvUserProviderId": "68724c8ef7ba75ab58acf289"}, {"_id": "68724c8ef7ba75ab58acf289", "firstName": "e2e-Practitioner1-FN", "middleName": "", "lastName": "e2e-Practitioner1-LN", "email": "<EMAIL>", "username": "e2e-Practitioner1", "profile": "5efdce1bcf29e3600494f922", "createdById": "687247b3f7ba75ab58acf21f", "adminUpdatedAt": "2025-07-12T11:52:46.253Z", "updatedById": "687247b3f7ba75ab58acf21f", "phone": "**********", "notes": "", "roles": [], "tokenUser": false, "isActive": true, "activeUpdatedAt": "2025-07-12T11:52:45.498Z", "acceptedTermsAndConditions": true, "isSelfService": false, "ssoLookup": "", "passwordHistory": [], "createdAt": "2025-07-12T11:52:46.254Z", "updatedAt": "2025-07-14T15:16:45.208Z", "__v": 0, "passwordBadCount": 0, "password": "$2a$13$2ABKqszPIcS.r/mjYSWmAe7RN.YyDgUMpzNfrx0pX.9z8p3KtdXYO", "passwordHashVersion": 2, "passwordLockoutUntil": null, "salt": "$2a$13$2ABKqszPIcS.r/mjYSWmAe", "isProvider": true, "passwordSettings": null, "lastLogin": "2025-07-14T15:16:45.208Z", "pwvUserProviderId": "68724c8ef7ba75ab58acf289"}]