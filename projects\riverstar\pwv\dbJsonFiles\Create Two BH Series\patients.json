[{"_id": "68724c92f7ba75ab58acf2c6", "ehrId": "588864", "providerUserId": "68724c8ef7ba75ab58acf289", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "address": "280 Washington Street", "address2": "", "city": "<PERSON>", "state": "Massachusetts", "zip": "01749", "phoneList": [{"phoneType": "Home", "phone": "**********", "_id": "68724c92f7ba75ab58acf2c7"}], "dob": "1950-10-10", "gender": "male", "contactPreference": "email", "insuranceType": "medicaid", "imported": false, "isActive": true, "sso": {"patientData": {"contacts": []}}, "createdAt": "2025-07-12T11:52:50.359Z", "updatedAt": "2025-07-12T11:52:50.359Z", "__v": 0}, {"_id": "68724d5af7ba75ab58ad0084", "ehrId": "586672", "providerUserId": "68724d55f7ba75ab58ad0047", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "address": "1740 <PERSON><PERSON>", "address2": "", "city": "Phoenix", "state": "Arizona", "zip": "85007", "phoneList": [{"phoneType": "Home", "phone": "**********", "_id": "68724d5af7ba75ab58ad0085"}], "dob": "1940-10-10", "gender": "female", "contactPreference": "sms", "insuranceType": "medicaid", "imported": false, "isActive": true, "sso": {"patientData": {"contacts": []}}, "createdAt": "2025-07-12T11:56:10.163Z", "updatedAt": "2025-07-12T11:56:10.163Z", "__v": 0}]