import Script from "@/app/core/script";
import Step from "@/app/core/step";
import h from "@/app/helpers/all";

export default class Test {
  description;
  sheet;
  id;
  runFlag;
  steps;

  constructor(test) {
    Object.assign(this, test);
    this.description = this.getDescription();
    this.setSteps();
  }

  getDescription() {
    const description = Object.entries(this).reduce((result, [key, value]) => {
      if (
        key !== "description" &&
        key !== "sheet" &&
        key !== "runFlag" &&
        key !== "steps"
      )
        result = `${result}${h.fromCamelCase(key)}: ${value}\n`;
      return result;
    }, "");
    return description;
  }

  setSteps() {
    this.steps = this.expand(this.steps);
    this.steps.unshift({
      do: "x",
      action: "Start Test",
    });
    this.steps.push({
      do: "x",
      action: "End Test",
    });
  }

  expand(steps) {
    const expandedSteps = steps.reduce((acc, step) => {
      if (
        step.do &&
        (h.compareNormalizedStrings(step.action, "Run Script") ||
          h.compareNormalizedStrings(step.action, "Call Script"))
      ) {
        const foundScript = new Script(step);
        if (step.values) {
          const runScriptValues = h
            .replaceEscapedCharactersAndQuotedStrings(step.values)
            .split(",");
          foundScript.replaceParams(runScriptValues);
        }
        let scriptSteps = foundScript.steps;
        if (step.do === "z") {
          scriptSteps[scriptSteps.length - 1]["do"] = "z";
        }
        scriptSteps.unshift({
          do: "x",
          action: `Start Script - ${step.target}`,
          rowNumber: step.rowNumber,
        });
        scriptSteps.push({
          do: "x",
          action: `End Script - ${step.target}`,
          rowNumber: step.rowNumber,
        });
        if (
          scriptSteps.find(
            (step) =>
              h.compareNormalizedStrings(step.action, "Run Script") ||
              h.compareNormalizedStrings(step.action, "Call Script")
          )
        ) {
          scriptSteps = this.expand(scriptSteps);
        }
        acc.push(...scriptSteps);
      } else {
        acc.push(step);
      }
      return acc;
    }, []);

    return expandedSteps;
  }

  run() {
    it(this.description, () => {
      Cypress.sdt.current.test = this;
      Cypress.sdt.results.executedTests.push(Cypress.sdt.current.test);
      this.steps.forEach((step, index) => {
        cy.then(() => {
          Cypress.sdt.current.step = new Step(step);
          Cypress.sdt.current.test.steps[index] = Cypress.sdt.current.step;
          Cypress.sdt.current.step.run();
        });
      });
    });
  }
}
