import h from "@/app/helpers/all";

export default class Parser {
  constructor(private readonly primitives = null) {}

  public parse(valueToParse: any): any {
    let parsedValue = null;
    if (!valueToParse) return parsedValue;
    parsedValue = this.parseValue(valueToParse);
    return parsedValue;
  }

  private parseValue(valueToParse) {
    let parsedValue;
    if (h.isString(valueToParse)) {
      parsedValue = this.parseText(valueToParse);
    } else parsedValue = this.parseObject(valueToParse);
    return parsedValue;
  }

  private parseText(valueToParse) {
    if (
      !valueToParse ||
      !h.isString(valueToParse) ||
      valueToParse.startsWith("@") ||
      !valueToParse.includes("[")
    ) {
      return valueToParse;
    }

    valueToParse = valueToParse.replace(/\]\[/g, ".");

    const primitiveValue = this.parsePrimitive(valueToParse)
    if (primitiveValue !== undefined) {
      return h.replaceEscapedCharacters(String(primitiveValue));
    }

    const references = this.findReferences(valueToParse);
    const parsedText = references.reduce((result, reference, index) => {
      const referenceValue = this.getReferenceValue(reference);
      if (h.isString(referenceValue)) {
        result = result.replace(references[index], referenceValue);
      } else result = referenceValue;
      return result;
    }, valueToParse);
    return parsedText;
  }

  parsePrimitive(valueToParse) {
    const argsBlock = valueToParse.match(/\(.*[\s\S]*?\)/)?.[0];
    const args = argsBlock?.replace(/^\((.*)\)$/, "$1").split(",");
    const key = valueToParse
      .replace(/^\[/, "")
      .replace(/]$/, "")
      .replace(argsBlock, "")
      .trim();
    const primitiveKey = h.getObjectSimilarKey(Cypress.sdt.primitives, key);
    if (primitiveKey) {
      return this.primitives[primitiveKey](...(args ?? []));
    }
    return undefined;
  }

  private parseObject(valueToParse) {
    if (h.isArray(valueToParse)) {
      return valueToParse.map((item) => this.parse(item));
    }
    const parsedObject = {};
    for (const [key, value] of Object.entries(valueToParse)) {
      if (value === null) {
        parsedObject[key] = null;
        continue;
      }
      if (typeof value === "object") {
        parsedObject[key] = this.parse(value);
      } else if (typeof value === "string") {
        parsedObject[key] = this.parseText(value);
      } else {
        parsedObject[key] = value;
      }
    }
    return parsedObject;
  }

  private findReferences(text: string): string[] {
    const regex = /\[([^\]]+)\]/g;
    const references: string[] = [];
    let match: RegExpExecArray | null;

    while ((match = regex.exec(text)) !== null) {
      references.push(`[${match[1]}]`);
    }
    return references;
  }

  private getReferenceValue(reference: string): any {
    const referenceKeys = this.getReferenceKeys(reference);
    if (Cypress.sdt.domain.checkCurrentEntityContainsField(referenceKeys[0])) {
      referenceKeys.unshift(Cypress.sdt.domain.getCurrentEntityId()!);
    }

    const entities = Cypress.sdt.domain.getAllEntities();
    const value = referenceKeys.reduce((result, part) => {
      let fieldValue = h.getObjectFieldValueWithNormalizedKey(result, part);
      if (
        (typeof fieldValue === "string" && fieldValue.includes("[")) ||
        typeof fieldValue === "object"
      ) {
        fieldValue = this.parse(fieldValue);
      }
      return fieldValue;
    }, entities);

    return value;
  }

  private getReferenceKeys(reference: string): string[] {
    return reference
      .replace(/^\[/g, "")
      .replace(/\]$/g, "")
      .replace(/\] *\[/g, ".")
      .split(".");
  }
}
