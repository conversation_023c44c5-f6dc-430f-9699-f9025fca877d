import Parser from "./parser";
import h from "@/app/helpers/all";

export default class Entry {
  expandedEntry = null;
  private readonly parser: Parser;

  constructor(private entry, private canBeList = false, primitives = null) {
    this.parser = new Parser(primitives);
    if (entry && h.isString(entry)) this.expandedEntry = this.expandEntry();
    else this.expandedEntry = entry;
  }

  expandEntry() {
    if (!this.entry) {
      this.expandedEntry = this.entry;
      return;
    }
    if (!this.canBeList) {
      this.expandedEntry = this.parser.parse(this.entry);
    } else {
      this.expandedEntry = this.expandEntryItems(this.entry) ?? [];
    }
    return this.expandedEntry;
  }

  expandEntryItems(entry) {
    if (!entry) return null;
    const entryElements = [];
    let current = '';
    let bracketDepth = 0;
  
    for (let i = 0; i < entry.length; i++) {
      const char = entry[i];
      if (char === '[') bracketDepth++;
      if (char === ']') bracketDepth--;
      if (char === ',' && bracketDepth === 0) {
        entryElements.push(current.trim());
        current = '';
        while (i + 1 < entry.length && /\s/.test(entry[i + 1])) i++;
        continue;
      }
      current += char;
    }
    if (current.trim()) entryElements.push(current.trim());
  
    const expandedEntryElements = entryElements.reduce(
      (result, entryElement) => {
        const expandedEntryElement = this.expandItem(entryElement);
        if (expandedEntryElement) result.push(expandedEntryElement);
        return result;
      },
      []
    );
    return expandedEntryElements;
  }

  expandItem(entryElement) {
    if (entryElement.startsWith("prop-")) return entryElement;
    if (entryElement.startsWith("icon")) return entryElement;
    if (entryElement.includes("=")) return entryElement;
    if (entryElement.includes(":")) {
      const index = entryElement.indexOf(":");
      const leftSide = entryElement.slice(0, index).trim();
      if (leftSide.startsWith("[")) return entryElement;
    }
    const expandedEntryElement = this.parser.parse(entryElement);
    return expandedEntryElement;
  }
}
