import Tester from "../tester";
import objectHelper from "../../../app/helpers/object";

const testSets = {
  getObjectFieldValueWithNormalizedKey: {
    getResult: function () {
      return objectHelper.getObjectFieldValueWithNormalizedKey(
        this.obj1,
        this.key1
      );
    },
    tests: {
      test01: {
        obj1: { a1: 1 },
        key1: "a1",
        expectedResult: 1,
      },
      test02: {
        obj1: { a1: 1 },
        key1: "x",
        expectedResult: null,
      },
      test03: {
        obj1: { a1: { b1: 1 } },
        key1: "a1.b1",
        expectedResult: 1,
      },
      test04: {
        obj1: { a1: { b1: 1, b2: { c1: 1, c2: 2 } } },
        key1: "a1.b2.c2",
        expectedResult: 2,
      },
    },
  },
  checkIfObjectsFieldsWithNormalizedKeysHaveSameValue: {
    getResult: function () {
      if (this.key2) {
        return objectHelper.checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
          this.user1,
          this.user2,
          this.key1,
          this.key2
        );
      } else if (this.key1) {
        return objectHelper.checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
          this.user1,
          this.user2,
          this.key1
        );
      } else {
        return objectHelper.checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
          this.user1,
          this.user2
        );
      }
    },
    tests: {
      test01: {
        user1: { username: 1 },
        user2: { lastname: 1 },
        key1: "username",
        key2: "lastname",
        expectedResult: true,
      },
      test02: {
        user1: { username: 1 },
        user2: { "User Name": 1 },
        key1: "username",
        expectedResult: true,
      },
      test03: {
        user1: { username: 1 },
        user2: { "User Name": 2 },
        key1: "username",
        expectedResult: false,
      },
      test04: {
        user1: { username: 1 },
        user2: { lastname: 1 },
        key1: "username",
        expectedResult: false,
      },
      test05: {
        user1: { username: 1 },
        user2: { username: 2 },
        expectedResult: false,
      },
      test06: {
        user1: { username: 1 },
        user2: { username: 1 },
        expectedResult: true,
      },
      test07: {
        user1: { username: 1 },
        user2: { lastname: 1 },
        expectedResult: false,
      },
    },
  },
  parseObjectWithTemplate: {
    getResult: function () {
      return objectHelper.parseObjectWithTemplate(this.input, this.template);
    },
    tests: {
      test01: {
        input: {},
        template: {},
        expectedResult: {},
      },
      test02: {
        input: { a: 1 },
        template: { A: 0 },
        expectedResult: { A: 1 },
      },
      test03: {
        input: { a: [1] },
        template: { A: [] },
        expectedResult: { A: [1] },
      },
      test04: {
        input: {},
        template: { A: [] },
        expectedResult: { A: [] },
      },
      test10: {
        input: { a: [1], b: { b1: 2 } },
        template: { A: [], B: { B1: 0 } },
        expectedResult: { A: [1], B: { B1: 2 } },
      },
      test11: {
        input: { a: [1], b: { b1: 2, b2: ["a", "b"] } },
        template: { A: [], B: { B1: 0, B2: [] } },
        expectedResult: { A: [1], B: { B1: 2, B2: ["a", "b"] } },
      },
    },
  },
  mergeObjects: {
    getResult: function () {
      return objectHelper.mergeObjects(this.objectA, this.objectB);
    },
    tests: {
      test01: {
        objectA: {},
        objectB: {},
        expectedResult: {},
      },
      test02: {
        objectA: { a: 1 },
        objectB: { b: 2 },
        expectedResult: { a: 1, b: 2 },
      },
      test03: {
        objectA: { a: 1 },
        objectB: { b: [1, 2] },
        expectedResult: { a: 1, b: [1, 2] },
      },
      test04: {
        objectA: { a: 1 },
        objectB: { b: [] },
        expectedResult: { a: 1, b: [] },
      },
      test05: {
        objectA: {
          a: [],
        },
        objectB: {
          a: [],
        },
        expectedResult: { a: [] },
      },
      test06: {
        objectA: {
          data: {},
          entities: {},
          timeActivity: [],
        },
        objectB: {
          timeActivity: [],
        },
        expectedResult: { data: {}, entities: {}, timeActivity: [] },
      },
    },
  },
};

const tester = new Tester(testSets, "objectHelper");
tester.run();
