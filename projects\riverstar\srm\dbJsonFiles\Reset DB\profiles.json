[{"_id": "63d028c01b1c42ac2cf7321f", "profileName": "Zero Day User", "menuItems": [], "isActive": true, "isDefault": false, "roles": ["ZeroDayUser"], "menuBehavior": "Open", "hideToolbarOptions": true, "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "646f93ff37a2735f14d4b9a0", "profileName": "FastSelfService", "menuItems": [], "isActive": true, "isDefault": false, "roles": ["FastSelfService"], "menuBehavior": "Open", "hideToolbarOptions": true, "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "5d813d0fc0e95a18aa475eb7", "profileName": "Agency User", "menuItems": [{"menuTitle": "Client Search", "menuRoute": "app/client/list", "isActive": true, "roles": ["AgencyUser"]}, {"menuTitle": "Referral Dashboard", "menuRoute": "app/referral/list", "isActive": true, "roles": ["AgencyUser"]}], "isActive": true, "isDefault": false, "roles": ["AgencyUser"], "menuBehavior": "Open", "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "5d813d0fc0e95a18aa475eb8", "profileName": "Organization User", "menuItems": [{"menuTitle": "Administration", "menuRoute": "", "isActive": true, "roles": [], "menuItems": [{"menuTitle": "User Administration", "menuRoute": "user-admin/list", "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "Client Administration", "menuRoute": "app/client/settings", "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "Community Administration", "menuRoute": "app/organization", "isActive": true, "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "SDoH Administration", "menuRoute": "app/dev/assessment-editor", "isActive": true, "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "iCarol API Query Test", "menuRoute": "app/rtm-query", "isActive": true, "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "Edit Homepage", "menuRoute": "app/home-edit", "isActive": true, "roles": ["Org<PERSON><PERSON><PERSON>"]}]}, {"menuTitle": "Report", "menuRoute": "app/report/list", "isActive": true, "roles": ["Reporting"]}], "isActive": true, "isDefault": true, "roles": ["ProfileAdmin", "UserAdmin", "AssessmentAdmin", "Reporting", "Org<PERSON><PERSON><PERSON>", "SysAdmin", "ManageAdminProfile"], "menuBehavior": "Open", "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "5d813d0fc0e95a18aa475eb6", "profileName": "System admin", "menuItems": [{"menuTitle": "Administration", "menuRoute": "", "isActive": true, "roles": [], "menuItems": [{"menuTitle": "User Administration", "menuRoute": "user-admin/list", "roles": []}]}], "isActive": true, "isDefault": true, "roles": ["ProfileAdmin", "UserAdmin", "ManageAdminProfile"], "menuBehavior": "Open", "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "5df8b04379d65e4a5f0887fa", "profileName": "Care Manager", "menuItems": [{"menuTitle": "Client Search", "menuRoute": "app/client/list", "roles": ["AgencyUser"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Practice Screenings & Referrals", "menuRoute": "app/care-team/care-team-practice-report", "roles": ["CareManager"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Specialty Practice Report", "menuRoute": "app/care-team/care-team-specialty-report", "roles": ["CareManager"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["CareManager", "AgencyUser"], "menuBehavior": "Open", "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "5df8b04379d65e4a5f0887fc", "profileName": "Organization BH User", "menuItems": [{"menuTitle": "Client Search", "menuRoute": "app/client/list", "roles": ["AgencyUser", "OrgUserBH"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Referral Dashboard", "menuRoute": "app/referral/list", "isActive": true, "roles": ["AgencyUser"]}], "isActive": true, "isDefault": false, "roles": ["CareManager", "AgencyUser", "OrgUserBH"], "menuBehavior": "Open", "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "5df8b04379d65e4a5f0887fb", "profileName": "Self-service", "menuItems": [], "isActive": true, "isDefault": false, "roles": ["SelfService"], "menuBehavior": "Open", "defaultRoute": "app/client/self-service", "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "5df8b04379d65e4a5f0887fd", "profileName": "Organization Admin User", "menuItems": [{"menuTitle": "Administration", "menuRoute": "", "isActive": true, "roles": [], "menuItems": [{"menuTitle": "User Administration", "menuRoute": "user-admin/list", "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "Client Administration", "menuRoute": "app/client/settings", "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "Community Administration", "menuRoute": "app/organization", "isActive": true, "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "SDoH Administration", "menuRoute": "app/dev/assessment-editor", "isActive": true, "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "iCarol API Query Test", "menuRoute": "app/rtm-query", "isActive": true, "roles": ["Org<PERSON><PERSON><PERSON>"]}, {"menuTitle": "Qr Admin - Zero Day", "menuRoute": "app/qr-admin/zero", "isActive": true}, {"menuTitle": "Qr Admin - Fast Self Service", "menuRoute": "app/qr-admin/fast", "isActive": true}, {"menuTitle": "Edit Homepage", "menuRoute": "app/home-edit", "isActive": true, "roles": ["Org<PERSON><PERSON><PERSON>"]}]}, {"menuTitle": "Report", "menuRoute": "app/report/list", "isActive": true, "roles": ["Reporting"]}, {"menuTitle": "Client Search", "menuRoute": "app/client/list", "roles": ["AgencyUser"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Screening Upload", "menuRoute": "app/client/assigned-agency-file", "roles": ["AgencyUser"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Screenings", "menuRoute": "app/client/assigned-clients", "roles": ["AgencyUser"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Assessments", "menuRoute": "app/client/assessments", "roles": ["AgencyUser", "AssessmentDashboard"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Referral Dashboard", "menuRoute": "app/referral/list", "isActive": true, "roles": ["AgencyUser"]}], "isActive": true, "isDefault": false, "roles": ["CareManager", "AgencyUser", "OrgAdminUser", "ProfileAdmin", "UserAdmin", "AssessmentAdmin", "Reporting", "Org<PERSON><PERSON><PERSON>", "SysAdmin", "ManageAdminProfile", "OrgUserBH", "AssessmentDashboard"], "menuBehavior": "Open", "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "5df8b04379d65e4a5f0887f1", "profileName": "SSOClients", "menuItems": [], "isActive": true, "isDefault": false, "roles": ["SSOClients"], "menuBehavior": "Open", "defaultRoute": "app/client/list", "createdAt": "2023-11-17T18:18:22.143Z", "updatedAt": "2023-11-17T18:18:22.143Z", "__v": 0}, {"_id": "604245803afe847d48606e4f", "profileName": "BH User", "menuItems": [{"menuTitle": "Client Search", "menuRoute": "app/client/list", "isActive": true, "roles": []}], "isActive": true, "isDefault": false, "roles": ["AgencyUser", "BHUser"], "menuBehavior": "Open", "__v": 0, "createdAt": "2023-11-17T18:18:22.879Z", "updatedAt": "2023-11-17T18:18:22.879Z"}, {"_id": "654c9d474454aee058d9d9fa", "profileName": "CHW", "menuItems": [{"menuTitle": "Assessments", "menuRoute": "app/client/assessments", "roles": ["AgencyUser", "AssessmentDashboard"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["AgencyUser", "AssessmentDashboard"], "menuBehavior": "Open", "__v": 0, "createdAt": "2023-11-17T18:18:22.879Z", "updatedAt": "2023-11-17T18:18:22.879Z"}]