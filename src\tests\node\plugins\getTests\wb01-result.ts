export default {
  tests: {
    Sheet1: [
      {
        Id: "7",
        Run: "x",
        Test: "2",
        "Use Case": "One Action with no 'Run' flag ",
        Steps: [
          {
            do: "x",
            action: "Start Test",
          },
          {
            do: "",
            action: "Action",
            rowNumber: "11",
          },
          {
            do: "x",
            action: "End Test",
          },
        ],
      },
      {
        Id: "13",
        Run: "x",
        Test: "3",
        "Use Case": "One Action with no Target and no Values",
        Steps: [
          {
            do: "x",
            action: "Start Test",
          },
          {
            do: "x",
            action: "Action",
            rowNumber: "17",
          },
          {
            do: "x",
            action: "End Test",
          },
        ],
      },
      {
        Id: "19",
        Run: "x",
        Test: "4",
        "Use Case":
          "One Action with no Target and no Values and another with no 'Run' flag.",
        Steps: [
          {
            do: "x",
            action: "Start Test",
          },
          {
            do: "",
            action: "Action 1",
            rowNumber: "23",
          },
          { title: "=====> Title", rowNumber: "24" },
          { subTitle: "Subtitle", rowNumber: "25" },
          {
            do: "x",
            action: "Action 2",
            rowNumber: "26",
          },
          {
            do: "x",
            action: "End Test",
          },
        ],
      },
      {
        Id: "28",
        Run: "x",
        Test: "5",
        "Use Case": "One stop Action",
        Steps: [
          {
            do: "x",
            action: "Start Test",
          },
          {
            do: "z",
            action: "Action 1",
            target: "Target1",
            values: "Values1",
            rowNumber: "32",
          },
          {
            do: "x",
            action: "End Test",
          },
        ],
      },
    ],
  },
  scripts: {
    Sheet1: [
      {
        Id: "35",
        Script: "Script1",
        Steps: [
          {
            do: "x",
            action: "Start Script",
          },
          {
            do: "x",
            action: "Action",
            rowNumber: "37",
          },
          {
            do: "x",
            action: "End Script",
          },
        ],
      },
    ],
    Scripts: [
      {
        Id: "4",
        Script: "Script2",
        Steps: [
          {
            do: "x",
            action: "Start Script",
          },
          {
            do: "",
            action: "Action",
            rowNumber: "6",
          },
          {
            do: "x",
            action: "End Script",
          },
        ],
      },
      {
        Id: "8",
        Script: "Script3",
        Steps: [
          {
            do: "x",
            action: "Start Script",
          },
          {
            do: "x",
            action: "Action",
            rowNumber: "10",
          },
          {
            do: "x",
            action: "End Script",
          },
        ],
      },
      {
        Id: "12",
        Script: "Script4",
        Input: "$Input",
        Steps: [
          {
            do: "x",
            action: "Start Script",
          },
          {
            do: "x",
            action: "Action",
            rowNumber: "15",
          },
          {
            do: "x",
            action: "End Script",
          },
        ],
      },
    ],
  },
  tables: {},
};
