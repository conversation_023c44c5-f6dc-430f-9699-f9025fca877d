export default class Tester {
  testSetLabel;
  testSetWithOnlyPropertyFound;
  testWithOnlyPropertyFound;
  foundError;

  constructor(private testSets, private objectName?) {
    let testSetsEntries = Object.entries(testSets);
    this.findOnlyProperty(testSetsEntries);
    testSetsEntries = this.filterTestsSets(testSetsEntries);
    testSetsEntries = testSetsEntries.map(([label, testSet]) => {
      testSet["tests"] = this.filterTests(testSet["tests"]);
      return [label, testSet];
    });
    this.testSets = Object.fromEntries(testSetsEntries);
  }

  findOnlyProperty(testSetsEntries) {
    this.testSetWithOnlyPropertyFound = testSetsEntries.find(
      ([_testSetLabel, testSet]) => (testSet["only"] ? true : false)
    )
      ? true
      : false;
    this.testWithOnlyPropertyFound = testSetsEntries.find(
      ([_testSetLabel, testSet]) =>
        Object.entries(testSet.tests).find(([_label, test]) =>
          test["only"] ? true : false
        )
    )
      ? true
      : false;
  }

  filterTestsSets(testSetsEntries) {
    // exclude test sets with 'skip' property
    testSetsEntries.filter(([_testSetLabel, testSet]) =>
      testSet["skip"] ? false : true
    );
    // only include test sets with 'only' property, if there are
    if (this.testSetWithOnlyPropertyFound)
      testSetsEntries = testSetsEntries.filter(([_label, testSet]) =>
        testSet["only"] ? true : false
      );
    return testSetsEntries;
  }

  filterTests(testsSetTests) {
    let testsEntries = Object.entries(testsSetTests);
    // exclude tests with 'skip' property
    testsEntries = testsEntries.filter(([_label, test]) =>
      test["skip"] ? false : true
    );
    // only include tests with 'only' property, if there are
    if (this.testWithOnlyPropertyFound)
      testsEntries = testsEntries.filter(([_label, test]) =>
        test["only"] ? true : false
      );
    return Object.fromEntries(testsEntries);
  }

  async run() {
    await Promise.all(
      Object.entries(this.testSets).map(async ([label, testSet]) => {
        testSet["testSetLabel"] = label;
        await this.runTestSet(testSet);
      })
    );
  }

  async runTestSet(testSet) {
    const tests = Object.entries(testSet.tests);
    await Promise.all(
      tests.map(async ([testLabel, test], index) => {
        test["testSetLabel"] = testSet["testSetLabel"];
        test["testLabel"] = testLabel;
        await this.runTest(test, testSet, index);
      })
    );
  }

  async runTest(test, testSet, index) {
    test.getResult = testSet.getResult;
    test.result = await test.getResult();
    if (
      typeof test["expectedResult"] === "string" &&
      test["expectedResult"].toLowerCase() === "none"
    )
      test["outcome"] = "OK";
    else
      test.outcome =
        JSON.stringify(test["expectedResult"]) === JSON.stringify(test.result)
          ? "OK"
          : "Error";
    this.logTestResult(test, index);
  }

  logTestResult(test, index) {
    if (index === 0) {
      this.foundError = false;
      if (this.objectName) {
        console.log(
          `\n*******************  ${this.objectName} ${test["testSetLabel"]}`
        );
      } else console.log(`\n*******************  ${test["testSetLabel"]}`);
    }
    console.log(`\n${test["testLabel"]}: ${test["outcome"]}`);
    if (test["outcome"] === "OK") {
      return;
    }
    console.log(JSON.stringify(test["expectedResult"]));
    console.log(JSON.stringify(test["result"]));
    this.foundError = true;
  }
}
