import Domain from "@/app/core/domain";
import Entry from "@/app/core/entry";
import Tester from "@/tests/node/tester";
import h from "@/app/helpers/all";

const testSets = {
  expandEntry: {
    getResult: function () {
      const domainData = { ...this.tables };
      const sdt = { domain: new Domain(domainData) };
      const entry = new Entry(sdt, this.entryValue, this.canBeList ?? true);
      Object.values(sdt.domain.entities).map((value) => {
        if (typeof value === "object") delete value["token"];
      });
      if (h.isArray(entry.expandedEntry))
        entry.expandedEntry = entry.expandedEntry.map((expandedEntry) => {
          if (h.isObject(expandedEntry)) delete expandedEntry["token"];
          if (h.isString(expandedEntry))
            expandedEntry = restoreSpecialCharacters(expandedEntry);
          return expandedEntry;
        });
      if (h.isString(entry.expandedEntry))
        entry.expandedEntry = restoreSpecialCharacters(entry.expandedEntry);

      return {
        expandedEntry: entry.expandedEntry,
        domainData: sdt.domain.entities,
      };
    },
    tests: {
      test01: {
        entryValue: "abc",
        tables: {},
        expectedResult: {
          expandedEntry: ["abc"],
          domainData: {},
        },
      },
      test02: {
        entryValue: "abc, xyz",
        tables: {},
        expectedResult: {
          expandedEntry: ["abc", "xyz"],
          domainData: {},
        },
      },
      test03: {
        entryValue: "abc\\, xyz",
        tables: {},
        expectedResult: {
          expandedEntry: ["abc, xyz"],
          domainData: {},
        },
      },
      test04: {
        entryValue: "abc\\: xyz",
        tables: {},
        expectedResult: {
          expandedEntry: ["abc\\: xyz"],
          domainData: {},
        },
      },
      test05: {
        entryValue: "abc.xyz",
        tables: {},
        expectedResult: {
          expandedEntry: ["abc.xyz"],
          domainData: {},
        },
      },
      test06: {
        entryValue: "abc, xyz",
        canBeList: false,
        tables: {},
        expectedResult: {
          expandedEntry: "abc, xyz",
          domainData: {},
        },
      },
      test07: {
        entryValue:
          '{\\[item="tabpanel"\\]:nth-of-type(3)} > {desktop-rstable} > {mat-paginator}',
        canBeList: false,
        tables: {},
        expectedResult: {
          expandedEntry:
            '{[item="tabpanel"]:nth-of-type(3)} > {desktop-rstable} > {mat-paginator}',
          domainData: {},
        },
      },
      test10: {
        entryValue: "[Entity1.Value]",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: {
          expandedEntry: ["value1"],
          domainData: {
            Entity1: { Id: "Entity1", Value: "value1" },
          },
        },
      },
      test11: {
        entryValue: "[Value]",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
          currentEntityId: "Entity1",
        },
        expectedResult: {
          expandedEntry: ["value1"],
          domainData: {
            Entity1: { Id: "Entity1", Value: "value1" },
            currentEntityId: "Entity1",
          },
        },
      },
      test12: {
        entryValue: "abc [Entity1.Value]",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: {
          expandedEntry: ["abc value1"],
          domainData: {
            Entity1: { Id: "Entity1", Value: "value1" },
          },
        },
      },
      test13: {
        entryValue: "abc, [Entity1.Value]",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: {
          expandedEntry: ["abc", "value1"],
          domainData: {
            Entity1: { Id: "Entity1", Value: "value1" },
          },
        },
      },
      test14: {
        entryValue: "[Entity1.ValueA] [Entity1.ValueB]",
        tables: {
          Entity1: { Id: "Entity1", ValueA: "valueA1", ValueB: "valueB1" },
        },
        expectedResult: {
          expandedEntry: ["valueA1 valueB1"],
          domainData: {
            Entity1: { Id: "Entity1", ValueA: "valueA1", ValueB: "valueB1" },
          },
        },
      },
      test15: {
        entryValue: "[Entity1]",
        tables: {
          Entity1: { Id: "Entity1", Value: { Name: "x" } },
        },
        expectedResult: {
          expandedEntry: [{ Id: "Entity1", Value: { Name: "x" } }],
          domainData: {
            Entity1: { Id: "Entity1", Value: { Name: "x" } },
          },
        },
      },
      test20: {
        entryValue: "[Entity1.Value]",
        tables: {
          Entity1: { Id: "Entity1", Value: "[Entity2.Value]" },
          Entity2: { Id: "Entity2", Value: "value2" },
        },
        expectedResult: {
          expandedEntry: ["value2"],
          domainData: {
            Entity1: { Id: "Entity1", Value: "[Entity2.Value]" },
            Entity2: { Id: "Entity2", Value: "value2" },
          },
        },
      },
      test21: {
        entryValue: "abc [Entity1.Value]",
        tables: {
          Entity1: { Id: "Entity1", Value: "[Entity2.Value]" },
          Entity2: { Id: "Entity2", Value: "value2" },
        },
        expectedResult: {
          expandedEntry: ["abc value2"],
          domainData: {
            Entity1: { Id: "Entity1", Value: "[Entity2.Value]" },
            Entity2: { Id: "Entity2", Value: "value2" },
          },
        },
      },
      test30: {
        entryValue: "[Entity1]",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: {
          expandedEntry: [{ Id: "Entity1", Value: "value1" }],
          domainData: {
            Entity1: { Id: "Entity1", Value: "value1" },
          },
        },
      },
      test31: {
        entryValue: "[Entity1], [Entity1.Value]",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: {
          expandedEntry: [{ Id: "Entity1", Value: "value1" }, "value1"],
          domainData: {
            Entity1: { Id: "Entity1", Value: "value1" },
          },
        },
      },
      test32: {
        entryValue: "[Entity1], [Entity2.Value]",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
          Entity2: { Id: "Entity2", Value: "[Entity1.Value]" },
        },
        expectedResult: {
          expandedEntry: [{ Id: "Entity1", Value: "value1" }, "value1"],
          domainData: {
            Entity1: { Id: "Entity1", Value: "value1" },
            Entity2: { Id: "Entity2", Value: "[Entity1.Value]" },
          },
        },
      },
      test33: {
        entryValue: "[entity1]",
        tables: {
          Entity1: {
            id: "Entity1",
            Value: "value1",
            Child: "[Entity2]",
            token: "1",
          },
          Entity2: { id: "Entity2", Value: "value2" },
        },
        expectedResult: {
          expandedEntry: [
            {
              id: "Entity1",
              Value: "value1",
              Child: { id: "Entity2", Value: "value2" },
            },
          ],
          domainData: {
            Entity1: {
              id: "Entity1",
              Value: "value1",
              Child: "[Entity2]",
            },
            Entity2: { id: "Entity2", Value: "value2" },
          },
        },
      },
      test40: {
        only: true,
        entryValue: '@([name="username"])',
        canBeList: false,
        tables: {},
        expectedResult: {
          expandedEntry: '@([name="username"])',
          domainData: {},
        },
      },
    },
  },
};

const tester = new Tester(testSets);
tester.run();

function restoreSpecialCharacters(text) {
  return text
    .replace(/§§§§§/g, "=")
    .replace(/§§§§/g, "]")
    .replace(/§§§/g, "[")
    .replace(/§§/g, ":")
    .replace(/§/g, ",");
}
