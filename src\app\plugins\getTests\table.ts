let currentSheetName;
let currentSheetContent;
let rowIndex;

export default function getSheetTable(sheetName, sheetContent) {
  currentSheetName = sheetName;
  currentSheetContent = sheetContent;
  rowIndex = 0;

  while (rowIndex < currentSheetContent.length) {
    const sheetRow = currentSheetContent[rowIndex];
    if (sheetRow[0]?.toLowerCase().trim() === "id") {
      const table = getTable();
      return table;
    }
    rowIndex++;
  }
}

function getTable() {
  while (rowIndex < currentSheetContent.length) {
    if (inTable()) {
      const table = [];
      currentSheetName = currentSheetName
        .replace(/ Data/i, "")
        .replace(/ Table/i, "");
      const keys = currentSheetContent[rowIndex]
        .filter((value) => typeof value === "string")
        .reduce((arr, value) => {
          arr.push(value);
          return arr;
        }, []);
      while (rowIndex < currentSheetContent.length) {
        const cellValue = currentSheetContent[rowIndex]?.[0];
        if (cellValue?.toLowerCase().trim() !== "id") {
          const rowObject = parseRow(keys, currentSheetContent[rowIndex]);
          table.push(rowObject);
        }
        rowIndex++;
      }
      if (table.length) return table;
    }
  }

  function parseRow(keys, row) {
    const rowObj = keys.reduce((obj, key, index) => {
      const value = row[index];
      if (value) obj[key] = value.toString().trim();
      return obj;
    }, {});
    return rowObj;
  }

  function inTable() {
    const cellValue = currentSheetContent[rowIndex]?.[0];
    return cellValue.toLowerCase().trim() === "id";
  }
}
