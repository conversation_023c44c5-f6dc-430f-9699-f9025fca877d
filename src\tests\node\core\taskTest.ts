import { MongoClient } from "mongodb";
import TaskHandler from "../../../app/handlers/taskHandler";

let db;
async function initializeDatabase() {
  const dbUrl = "mongodb://127.0.0.1:27055/inMemoryDB";
  const mongoClient = await MongoClient.connect(dbUrl as string);
  db = mongoClient.db();
}

async function test() {
  await initializeDatabase();

  const task = new TaskHandler(db);
  const coreTasks = task.getCoreTasks();

  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.error(
      "Please provide a method name and its parameters as arguments"
    );
    process.exit(1);
  }

  const [methodName, ...params] = args;

  const result = await (coreTasks as any)[methodName](...params);
  console.log(
    `Method ${methodName} executed successfully with parameters: ${JSON.stringify(
      params
    )}`
  );
  if (result !== undefined) {
    console.log(`Result: ${JSON.stringify(result)}`);
  }
}

test();
