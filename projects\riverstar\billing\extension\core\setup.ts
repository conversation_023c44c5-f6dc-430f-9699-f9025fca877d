import coreSetup from "@/app/core/setup";
import qbHandler from "../handlers/qbHandler";
import riverstarSdt from "../../../.organization/sdt";

export default {
  before() {
    cy.then(() => coreSetup.before()).then(() => qbHandler.setRealmId());
  },
  beforeEach() {
    cy.then(() => coreSetup.beforeEach())
      .then(() => riverstarSdt.setup.beforeEach())
      .then(() => {
        Cypress.sdt.actions["Delete All QB Invoices"]();
        Cypress.sdt.actions["Set All QB Customers Inactive"]();
        Cypress.sdt.actions["Set All QB Employees Inactive"]();
      });
  },
  afterEach() {
    coreSetup.afterEach();
  },
  after() {
    coreSetup.after();
  },
};
