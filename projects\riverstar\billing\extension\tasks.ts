import h from "../../../../src/app/helpers/all";
import organizationTasks from "../../.organization/tasks";

const dbTasks = {
  dbReadEmployee: async ({ firstName, lastName }) => {
    h.consoleLog("taskHandler dbReadEmployee 1", {
      firstName,
      lastName,
    });
    const dbEmployee = await global.db
      .collection("employees")
      .findOne({ firstName, lastName });
    h.consoleLog("taskHandler dbReadEmployee 2", { dbEmployee });
    return dbEmployee;
  },

  dbReadEmployeeWithId: async (employeeId: string) => {
    return await global.db.collection("employees").findOne({
      _id: employeeId,
    });
  },

  dbReadCustomerWithId: async (customerId) => {
    return await global.db.collection("customers").findOne({
      _id: customerId,
    });
  },

  dbReadCustomer: async (customer) => {
    return await global.db.collection("customers").findOne(customer);
  },

  dbReadAllTimeEntries: async () => {
    const timeEntries = await global.db
      .collection("timeentries")
      .find()
      .toArray();
    return await Promise.all(
      timeEntries.map(async (timeEntry) => {
        timeEntry.customer = await global.db.collection("customers").findOne({
          _id: timeEntry.customerId,
        });
        timeEntry.employee = await global.db.collection("employees").findOne({
          _id: timeEntry.employeeId,
        });
        timeEntry.approvalUser = await global.db
          .collection("authusers")
          .findOne({
            _id: timeEntry.approvalUserId,
          });
        return timeEntry;
      })
    );
  },
};

const qbTasks = {
  getAccessToken: (refreshToken) => {
    const options = {
      hostname: "oauth.platform.intuit.com",
      path: "/oauth2/v1/tokens/bearer",
      method: "POST",
      headers: {
        Authorization:
          "Basic QUJOT1RRUHRKc3paeHJDcTRvcks4azMxMDlVODRBVE5KWk1LU1JPUXMyWjRGQ2Q0TEc6d2U3TUphU3d3aERqRGhFdEFqZTRZMUZ2Mjl5MVZ5TTJ1ZFljYmxydg==",
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
    };
    const body = `grant_type=refresh_token&refresh_token=${refreshToken}`;
    h.consoleLog("tasks getAccessToken 1", { body });
    return h.httpsRequest(options, body).then((response) => {
      h.consoleLog("tasks getAccessToken 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      return response;
    });
  },

  dbCreateQuickbooks: async ({ refreshToken, accessToken }) => {
    h.consoleLog("tasks dbCreateQuickbooks 1", {
      refreshToken,
      accessToken,
    });
    return await global.db.collection("quickbooks").insertOne({
      token: {
        refresh_token: refreshToken,
        access_token: accessToken,
      },
      realmid: "4620816365230372500",
    });
  },

  getCustomersBag: ({ accessToken, startPosition, maxResults }) => {
    const query = encodeURIComponent(
      `select * from Customer STARTPOSITION ${startPosition} MAXRESULTS ${maxResults}`
    );
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: `/v3/company/4620816365230372500/query?query=${query}&minorversion=65`,
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "text/plain",
        Accept: "application/json",
      },
    };
    h.consoleLog("tasks getCustomersBag 1", { options });
    return h.httpsRequest(options).then((response) => {
      h.consoleLog("tasks getCustomersBag 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      const customers = response["QueryResponse"]?.Customer;
      h.consoleLog("tasks getCustomersBag 3", {
        customers: customers,
      });
      return customers ?? null;
    });
  },

  getCustomer: ({ accessToken, customerDisplayName }) => {
    const query = encodeURIComponent(
      `select * from Customer Where DisplayName = '${customerDisplayName}'`
    );
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: `/v3/company/4620816365230372500/query?query=${query}&minorversion=65`,
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "text/plain",
        Accept: "application/json",
      },
    };
    h.consoleLog("tasks getCustomer 1", { query, options });
    return h.httpsRequest(options).then((response) => {
      h.consoleLog("tasks getCustomer 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      const customer = response["QueryResponse"].Customer[0];
      h.consoleLog("qbApiHandler getCustomer 2", { response }, { customer });
      return customer;
    });
  },

  setCustomer: ({ accessToken, customer }) => {
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: "/v3/company/4620816365230372500/customer?minorversion=65",
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    };
    const body = JSON.stringify(customer);
    h.consoleLog("tasks setCustomer 1", { options, body });
    return h.httpsRequest(options, body).then((response) => {
      h.consoleLog("tasks setCustomer 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      return response;
    });
  },

  getEmployees: (accessToken) => {
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: "/v3/company/4620816365230372500/query?query=select%20%2A%20from%20Employee&minorversion=65",
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "text/plain",
        Accept: "application/json",
      },
    };
    h.consoleLog("tasks getEmployees 1", { options });
    return h.httpsRequest(options).then((response) => {
      h.consoleLog("tasks getEmployees 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      const employees = response["QueryResponse"].Employee ?? null;
      h.consoleLog("tasks getEmployees 3", {
        employees: employees,
      });
      return employees;
    });
  },

  getEmployee: ({ accessToken, employeeDisplayName }) => {
    const query = encodeURIComponent(
      `select * from Employee Where DisplayName = '${employeeDisplayName}'`
    );
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: `/v3/company/4620816365230372500/query?query=${query}&minorversion=65`,
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "text/plain",
        Accept: "application/json",
      },
    };
    h.consoleLog("tasks getEmployee 1", { query, options });
    return h.httpsRequest(options).then((response) => {
      h.consoleLog("qbApiHandler getEmployee 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      const employee = response["QueryResponse"].Employee[0];
      h.consoleLog("qbApiHandler getEmployee 3", { employee });
      return employee;
    });
  },

  setEmployee: ({ accessToken, employee }) => {
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: "/v3/company/4620816365230372500/employee?minorversion=65",
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    };
    const body = JSON.stringify(employee);
    h.consoleLog("tasks setEmployee 1", { options, body });
    return h.httpsRequest(options, body).then((response) => {
      h.consoleLog("tasks setEmployee 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      return response;
    });
  },

  getVendors: (accessToken) => {
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: "/v3/company/4620816365230372500/query?query=select%20%2A%20from%20vendor&minorversion=65",
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "text/plain",
        Accept: "application/json",
      },
    };
    h.consoleLog("tasks getVendors 1", { options });
    return h.httpsRequest(options).then((response) => {
      h.consoleLog("tasks getVendors 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      const vendors = response["QueryResponse"].Vendor;
      h.consoleLog("tasks getVendors 3", {
        employees: vendors,
      });
      return vendors;
    });
  },

  setVendor: ({ accessToken, vendor }) => {
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: "/v3/company/4620816365230372500/Vendor?minorversion=65",
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    };
    const body = JSON.stringify(vendor);
    h.consoleLog("tasks setVendor 1", { options }, { body });
    return h.httpsRequest(options, body).then((response) => {
      h.consoleLog("tasks setVendor 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      return response;
    });
  },

  getTimeEntries: (accessToken) => {
    const query = encodeURIComponent("select * from TimeActivity");
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: `/v3/company/4620816365230372500/query?query=${query}&minorversion=65`,
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "text/plain",
        Accept: "application/json",
      },
    };

    h.consoleLog("tasks getQbTimeEntries 1", { options });
    return h.httpsRequest(options).then((response) => {
      h.consoleLog("tasks getQbTimeEntries 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      const timeEntries = response["QueryResponse"].TimeActivity ?? null;
      return timeEntries;
    });
  },

  deleteTimeEntry: ({ accessToken, timeEntry }) => {
    h.consoleLog("tasks deleteTimeEntry 0", { timeEntry });
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: "/v3/company/4620816365230372500/timeactivity?operation=delete&minorversion=65",
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    };
    const body = JSON.stringify({
      SyncToken: timeEntry.SyncToken,
      Id: timeEntry.Id,
    });
    h.consoleLog("tasks deleteTimeEntry 1", { options }, { body });
    return h.httpsRequest(options, body).then((response) => {
      h.consoleLog("tasks deleteTimeEntry 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      return response;
    });
  },

  getInvoices: (accessToken) => {
    const query = encodeURIComponent(
      "SELECT * FROM Invoice WHERE Balance > '0'"
    );
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: `/v3/company/4620816365230372500/query?query=${query}&minorversion=75`,
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "text/plain",
        Accept: "application/json",
      },
    };

    h.consoleLog("tasks getInvoices 1", { options });
    return h.httpsRequest(options).then((response) => {
      h.consoleLog("tasks getInvoices 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      const invoices = response["QueryResponse"].Invoice ?? null;
      return invoices;
    });
  },

  deleteInvoice: ({ accessToken, invoice }) => {
    h.consoleLog("tasks deleteInvoice 0", { invoice });
    const options = {
      hostname: "sandbox-quickbooks.api.intuit.com",
      path: "/v3/company/4620816365230372500/invoice?operation=delete&minorversion=75",
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    };
    const body = JSON.stringify({
      SyncToken: invoice.SyncToken,
      Id: invoice.Id,
    });
    h.consoleLog("tasks deleteInvoice 1", { options }, { body });
    return h.httpsRequest(options, body).then((response) => {
      h.consoleLog("tasks deleteInvoice 2", { response });
      if (response["error"]) throw new Error(response["error"]);
      return response;
    });
  },
};

export default {
  ...organizationTasks,
  ...dbTasks,
  ...qbTasks,
};
