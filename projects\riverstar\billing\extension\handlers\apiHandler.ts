import cypressHelper from "@/app/helpers/cypress";

const ENDPOINTS = {
  CUSTOMER: "app-api/customer",
  EMPLOYEE: "app-api/employee",
} as const;

type EntityType = "customer" | "customers" | "employee" | "employees";

export default {
  syncWithQb: (entityToSync?: string) => {
    const normalizedEntity = entityToSync?.toLowerCase().trim();

    const syncOperations = {
      customer: () => syncCustomers(),
      customers: () => syncCustomers(),
      employee: () => syncEmployees(),
      employees: () => syncEmployees(),
    };

    // If no entity specified, sync all
    if (!normalizedEntity) {
      return Promise.all([syncCustomers(), syncEmployees()]);
    }

    // Sync specific entity
    const syncFn = syncOperations[normalizedEntity as EntityType];
    return syncFn?.();
  },

  associateQbEmployeeToUser: (employee: any, user: any) => {
    if (!employee?._id || !user?._id) {
      throw new Error("Employee and User IDs are required");
    }
    const updatedEmployee = {
      ...employee,
      commissionLevelList: [],
      pwvCommission: false,
      billingUser: null,
      billingUserId: user._id,
      commission: "1",
    };
    const options = {
      method: "PUT",
      url: `${ENDPOINTS.EMPLOYEE}/${employee._id}`,
      body: updatedEmployee,
      failOnStatusCode: true,
    };
    return cypressHelper.apiCall(options).then((response) => response["body"]);
  },
};

function syncCustomers() {
  const options = {
    method: "GET",
    url: `${ENDPOINTS.CUSTOMER}/findWithSync`,
    failOnStatusCode: true,
  };
  return cypressHelper.apiCall(options).then((response) => response["body"]);
}

function syncEmployees() {
  const options = {
    method: "GET",
    url: `${ENDPOINTS.EMPLOYEE}/findWithSync`,
    failOnStatusCode: true,
  };
  return cypressHelper.apiCall(options).then((response) => response["body"]);
}
