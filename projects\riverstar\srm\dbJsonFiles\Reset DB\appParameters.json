[{"_id": "5c94e8f0ce9e596dbfc7a303", "value": "2000-02-01T06:00:00.000Z", "__v": 0}, {"_id": "63e68c4c2a84c791794e3203", "key": "clientTabSettings", "value": {"name": "Additional Information", "visible": true, "fields": [{"fieldId": "f0e2e90e-abc4-4ee0-ac69-e40791be48b8", "fieldText": "Race", "fieldType": "multi", "fieldOptions": ["American Indian or Alaska Native", "Asian", "Black or African American", "Native Hawaiian or Other Pacific Islander", "White"], "fieldParentId": null, "fieldParentOption": null, "visible": true, "required": false}, {"fieldId": "fed830ef-c27b-48ce-b74b-3f3c916650b8", "fieldText": "Ethnicity", "fieldType": "multi", "fieldOptions": ["Hispanic or Latino", "Middle Eastern or North African", "White/Caucasian"], "fieldParentId": null, "fieldParentOption": null, "visible": true, "required": false}, {"fieldId": "564bed0d-bbc4-4185-ab82-5f20e91ad26a", "fieldText": "Are you interested in any of the follow outdoor activities?", "fieldType": "multi", "fieldOptions": ["Hunting", "Fishing", "Camping"], "fieldParentId": null, "fieldParentOption": null, "visible": true, "required": false}, {"fieldId": "564bed0d-bbc4-4185-ab82-5f20e91ad26b", "fieldText": "Hunting with family or as an individual?", "fieldType": "radio", "fieldOptions": ["As an individual pursuit", "As a family pursuit"], "fieldParentId": "564bed0d-bbc4-4185-ab82-5f20e91ad26a", "fieldParentOption": "Hunting", "visible": true, "required": false}, {"fieldId": "564bed0d-bbc4-4185-ab82-5f20e91ad26c", "fieldText": "Fishing with family or as an individual?", "fieldType": "radio", "fieldOptions": ["As an individual pursuit", "As a family pursuit"], "fieldParentId": "564bed0d-bbc4-4185-ab82-5f20e91ad26a", "fieldParentOption": "Fishing", "visible": true, "required": false}, {"fieldId": "564bed0d-bbc4-4185-ab82-5f20e91ad26d", "fieldText": "Camping with family or as an individual?", "fieldType": "radio", "fieldOptions": ["As an individual pursuit", "As a family pursuit"], "fieldParentId": "564bed0d-bbc4-4185-ab82-5f20e91ad26a", "fieldParentOption": "Camping", "visible": true, "required": false}, {"fieldId": "564bed0d-bbc4-4185-ab82-5f20e91ad26e", "fieldText": "Are you interested in learning about job training opportunities in any of these trades?", "fieldType": "multi", "fieldOptions": ["Construction ", "Heavy Equipment", "Culinary Arts"], "fieldParentId": null, "fieldParentOption": null, "visible": true, "required": false}]}, "__v": 0}]