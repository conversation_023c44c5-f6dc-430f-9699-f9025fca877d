import cypressHelper from '@/app/helpers/cypress';

export default class Screenshot {
  public static takeScreenshot(): void {
    cypressHelper.delay();
    if (Cypress.sdt.current.step.rowNumber) {
      Cypress.sdt.current.step.screenshotFilename = `[${Cypress.sdt.current.test.sheet}][${Cypress.sdt.current.step.rowNumber}].png`;
    } else {
      Cypress.sdt.current.step.screenshotFilename = `[${Cypress.sdt.current.test.sheet}].png`;
    }
    cy.screenshot(Cypress.sdt.current.step.screenshotFilename, {
      disableTimersAndAnimations: true,
      onAfterScreenshot: (_$el, props) => {
        Cypress.sdt.current.step["screenshotPath"] = props.path;
      },
    });
  }
}
