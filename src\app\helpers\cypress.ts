import h from "@/app/helpers/all";

export default {
  delay(seconds: number = 2): Cypress.Chainable {
    return cy.wait(1000 * seconds);
  },

  apiCall(options, body?) {
    return cy.request(options, body).then((response) => {
      return response;
    });
  },

  logTitle(title: string, titleLevel = h.titleLevel1, boldTitle = true) {
    let titleMarker;
    let titleSpacer;
    if (!titleLevel) {
      titleMarker = "■".repeat(5);
      titleSpacer = "\u00A0".repeat(5);
    } else {
      titleMarker = "■".repeat(titleLevel * 10);
      titleSpacer = "\u00A0".repeat(titleLevel * 10);
    }
    return cy.then(() =>
      title.split(/\n/g).forEach((part, index) => {
        if (index === 0) {
          if (boldTitle) cy.log(`**${titleMarker} ${part}**`);
          else cy.log(`${titleMarker} ${part}`);
        } else cy.log(`.${titleSpacer.slice(0, -1)} ${part}`);
      })
    );
  },

  setError(message: string, value: any): void {
    this.clog(`${message}: ${value}`);
    throw new Error(`${message}: ${value}`);
  },

  clog(...args: any[]): void {
    if (!Cypress.sdt.config.consoleLog) {
      return;
    }

    this.checkSdtForCircularReferences();

    const textFormat = "color: grey; font-size: 14px; font-weight: bold";

    if (args[0]) {
      console.log("=".repeat(50));
    }

    args.forEach((value: any, index: number) => {
      if (value)
        if (index === 0) {
          console.log("%c" + value, textFormat);
        } else if (typeof value === "object") {
          console.dir(value, { depth: 3, colors: true });
        } else {
          console.log(value);
        }
    });

    if (Cypress.sdt.config.stackTrace) {
      const stackTraceFormat = "color: grey; font-size: 10px";
      console.log("%c" + this.getStackTrace(), stackTraceFormat);
    }
  },

  checkSdtForCircularReferences() {
    const hasCircular = h.hasCircularReferences(Cypress.sdt);
    if (hasCircular) console.log("Found circular references in Cypress.sdt");
    return hasCircular;
  },
};
