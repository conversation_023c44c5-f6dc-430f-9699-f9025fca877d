export default {
  randomInteger(min: number = 100, max: number = 999): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  randomNumber(length: number = 4): string {
    const num = Math.floor(Math.random() * Math.pow(10, length));
    return num.toString().padStart(length, '0');
  },

  randomString(length: number = 4, prefix?: string, suffix?: string): string {
    let str = '';
    if (prefix) {
      str = prefix + '-';
    }
    str += this.randomNumber(length);
    if (suffix) {
      str += '-' + suffix;
    }
    return str;
  },
};
