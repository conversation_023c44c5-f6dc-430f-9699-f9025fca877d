import Element from "@/app/core/element";
import Entry from "@/app/core/entry";
import h from "@/app/helpers/all";
import parseLabelAliases from "./alias";

export default class Target {
  isVisible = true;
  descriptor;
  currentEntityFieldValue;
  element;
  elements = [];
  lastElement;

  constructor(private label = "", public isPartial = false) {
    if (this.label?.startsWith(Cypress.sdt.config.recorderFileSelector)) return;
    this.parse(label);
  }

  parse(label) {
    if (!label) {
      this.label = "";
      return;
    }
    if (label.match(/not *visible/gi)) {
      this.isVisible = false;
      label = label.replace(/not *visible/gi, "");
    }
    label = h.replaceEscapedCharactersAndQuotedStrings(label);
    const entry = new Entry(label);
    const expandedLabel = entry.expandedEntry ?? "";
    const parsedLabel = parseLabelAliases(
      expandedLabel,
      Cypress.sdt.data.aliases,
      Cypress.sdt.results.usedAliases
    );
    this.setElements(parsedLabel);
  }

  setElements(label) {
    const elementLabels = label?.split(/ *> *(?![^{]*})/);
    elementLabels?.forEach((elementLabel, index) => {
      const isLast = index === elementLabels.length - 1;
      const element = new Element({
        label: elementLabel,
        isVisible: this.isVisible,
        isLast,
      });
      this.elements.push(element);
    });
    this.lastElement = this.elements.at(-1);
    this.lastElement.isLast = true;
  }

  get(param?) {
    const parent = param?.parent ?? cy.get("body");
    const elementIndex = param?.elementIndex ?? 0;
    const validation = param?.validation;

    if (this.label?.startsWith(Cypress.sdt.config.recorderFileSelector))
      return cy.get(this.label.substring(1));

    const element = this.elements[elementIndex];

    if (element?.label.startsWith("@")) {
      const regex = /@\((.*?)\)/;
      const match = element.label.match(regex);
      return cy.get(match[1]);
    }

    const lastElementIndex = this.elements.length - 1;

    return element.getElement(parent, validation).then((foundElement) => {
      if (elementIndex < lastElementIndex) {
        return this.get({
          parent: cy.get(foundElement),
          elementIndex: elementIndex + 1,
          validation,
        });
      }
      return cy.get(foundElement);
    });
  }

  lastElementNameIncludes(elementName) {
    const lastElementName = this.lastElement?.name.replace(/{|}/g, "");
    if (!lastElementName) {
      return false;
    }
    return h.includesNormalizedString(lastElementName, elementName);
  }

  lastElementNameIs(elementName) {
    const lastElementName = this.lastElement?.name.replace(/{|}/g, "");
    if (!lastElementName) {
      return false;
    }
    return h.compareNormalizedStrings(lastElementName, elementName);
  }
}
