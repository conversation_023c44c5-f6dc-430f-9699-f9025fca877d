#!/bin/bash

echo "======> Script Driven Tests"

echo
echo "======> Get configuration"
echo
RUN_FOLDER=$(dirname "$0")
SDT_SCRIPT=$(cat ./config.json | grep '"sdtScript"' | awk -F'"' '{print $4}')
export SDT_RUN_FOLDER=$RUN_FOLDER

echo
echo "======> Terminate pending node processes"
echo
cmd "/C TASKKILL /IM node.exe /F 2>nul"

echo
echo "======> Install and start main application"
echo
cd ../../../../server
npm start &

echo
echo "======> Start SDT"
echo
cd ../sdt
npm run $SDT_SCRIPT
