import { ObjectId } from "mongodb";
import fs from "fs-extra";
import path from "path";

async function setDb(folderName) {
  const setObjectId = (doc) => {
    const processNestedObjects = (obj) => {
      Object.keys(obj).forEach((key) => {
        if (typeof obj[key] === "object" && obj[key] !== null) {
          processNestedObjects(obj[key]);
        }
        if (
          key === "_id" ||
          (key.endsWith("Id") && key.length === 24) ||
          key === "profile"
        ) {
          obj[key] = new ObjectId(obj[key] as string);
        }
      });
    };

    processNestedObjects(doc);
    return doc;
  };
  const files = fs.readdirSync(folderName);
  for (const file of files) {
    const filePath = path.join(folderName, file);
    const fileContents = fs.readFileSync(filePath, "utf8");
    let documents = JSON.parse(fileContents);
    documents = documents.map((doc) => {
      return setObjectId(doc);
    });
  }

  return null;
}

async function test() {
  const folderName = `C:/Data/rs-projects/sdt/run/projects/riverstar/dbJsonFiles/Reset DB`;
  await setDb(folderName);
}

test();
