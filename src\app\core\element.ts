import coreLocators from "@/app/core/locators";
import h from "@/app/helpers/all";

export default class Element {
  label;
  isVisible;
  isLast;
  key;
  content;
  icon;
  index;
  name;
  selector;
  locator;
  root;

  constructor({ label, isVisible = true, isLast = true }) {
    this.label = label;
    this.isVisible = isVisible;
    this.isLast = isLast;
    if (this.label.match(/not *visible/i)) {
      this.isVisible = false;
      this.label = this.label.replace(/not *visible/gi, "");
    }
    this.label = h.restoreQuotedStringsAndEscapedCharacters(this.label);
    this.setElement();
    this.setIndex();
    this.setContent();
  }

  setElement() {
    // Extract element name
    this.name = this.label?.match(/{.*}/)?.[0];
    if (!this.name) {
      this.selector = this.label;
      return;
    }

    // Find matching element in elements collection
    const elementToFind = this.name
      .replace(/{|}/g, "")
      .replace(/\([^)]*\)/g, "");
    this.key = h.getNormalizedValueInList(
      elementToFind,
      Object.keys(Cypress.sdt.data.elements)
    );

    if (!this.key) {
      this.selector = this.name.replace(/{|}/g, "");
    } else {
      // Extract arguments
      const argsBlock = this.name.match(/\(.*\)/)?.[0];
      // Parse arguments if present
      const args = argsBlock
        ? argsBlock.replace(/[()]/g, "").split(/ *, */)
        : [];
      // Track used Elements
      if (!Cypress.sdt.results.usedElements?.includes(this.key)) {
        Cypress.sdt.results.usedElements.push(this.key);
        Cypress.sdt.results.usedElements.sort();
      }

      // Get the found element properties
      const foundElement = Cypress.sdt.data.elements[this.key];
      this.selector = foundElement.selector;
      this.locator = foundElement.locator || "byContent";
      this.root = foundElement.root || "body";

      // Replace placeholders with arguments
      let index = 0;
      this.selector = this.selector.replace(/\$[\w\d]+/g, (match) => {
        const replacement = args[index] ?? match;
        index++;
        return replacement;
      });
    }

    // Add :visible to selector if needed
    if (this.isVisible) this.selector = this.selector + ":visible";
  }

  setIndex() {
    const match = this.label.match(/#(.*)#/);
    this.index = match?.[1];
    this.label = this.label.replace(match?.[0], "");
  }

  setContent() {
    this.content = this.label?.replace(/{.*}/, "").replace(/#.*#/, "").trim();
    if (this.content) this.setContentWithIcons();
  }

  setContentWithIcons() {
    this.getIcon(this.key, this.content);
    const leftIcon = this.icon?.left ? this.icon?.left : "";
    const rightIcon = this.icon?.right ? this.icon?.right : "";
    this.content = leftIcon + this.content + rightIcon;
  }

  getIcon(element, content) {
    if (
      Cypress.sdt.current.step.icon?.left ||
      Cypress.sdt.current.step.icon?.right
    ) {
      this.icon = {
        left: Cypress.sdt.current.step.icon.left,
        right: Cypress.sdt.current.step.icon.right,
      };
    } else {
      const iconKey = Cypress.sdt.icons[element]?.[content];
      if (iconKey)
        this.icon = {
          left: iconKey.left,
          right: iconKey.right,
        };
    }
  }

  getElement(parent, validation?) {
    if (!this.locator) {
      return parent.find(this.selector).should(($elem) => {
        if (this.isLast && validation) validation($elem);
      });
    }

    const locators = { ...coreLocators, ...Cypress.sdt.extension?.locators };
    const locator = h.getObjectFieldValueWithNormalizedKey(
      locators,
      this.locator
    );

    return locator(parent, this, validation);
  }
}
