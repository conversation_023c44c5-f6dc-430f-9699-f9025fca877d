import Tester from "@/tests/node/tester";
import expectedResult01 from "@/tests/node/plugins/getTests/wb01-result";
import expectedResult02 from "@/tests/node/plugins/getTests/wb02-result";
import expectedResult03 from "@/tests/node/plugins/getTests/wb03-result";
import expectedResult04 from "@/tests/node/plugins/getTests/wb04-result";
import expectedResult05 from "@/tests/node/plugins/getTests/wb05-result";
import expectedResult06 from "@/tests/node/plugins/getTests/wb06-result";
import expectedResult07 from "@/tests/node/plugins/getTests/wb07-result";
import expectedResult08 from "@/tests/node/plugins/getTests/wb08-result";
import expectedResult09 from "@/tests/node/plugins/getTests/wb09-result";
import expectedResult10 from "@/tests/node/plugins/getTests/wb10-result";
import parseExcelFile from "@/app/plugins/getTests/parseExcelFile";
import path from "path";

const dataFile01 = "./wb01.xlsx";
const dataFile02 = "./wb02.xlsx";
const dataFile03 = "./wb03.xlsx";
const dataFile04 = "./wb04.xlsx";
const dataFile05 = "./wb05.xlsx";
const dataFile06 = "./wb06.xlsx";
const dataFile07 = "./wb07.xlsx";
const dataFile08 = "./wb08.xlsx";
const dataFile09 = "./wb09.xlsx";
const dataFile10 = "./wb10.xlsx";
const pwv = "../../../../../projects/pwv/run/dev/pwv.xlsx";
const riverstar = "../../../../run/riverstar/riverstar.xlsx";

const testSets = {
  parseExcelFile: {
    getResult: async function () {
      const parsedData = await parseExcelFile(this.wb);
      return parsedData;
    },
    tests: {
      test01: {
        wb: path.join(__dirname, dataFile01),
        expectedResult: expectedResult01,
      },
      test02: {
        wb: path.join(__dirname, dataFile02),
        expectedResult: expectedResult02,
      },
      test03: {
        wb: path.join(__dirname, dataFile03),
        expectedResult: expectedResult03,
      },
      test04: {
        wb: path.join(__dirname, dataFile04),
        expectedResult: expectedResult04,
      },
      test05: {
        wb: path.join(__dirname, dataFile05),
        expectedResult: expectedResult05,
      },
      test06: {
        wb: path.join(__dirname, dataFile06),
        expectedResult: expectedResult06,
      },
      test07: {
        wb: path.join(__dirname, dataFile07),
        expectedResult: expectedResult07,
      },
      test08: {
        wb: path.join(__dirname, dataFile08),
        expectedResult: expectedResult08,
      },
      test09: {
        wb: path.join(__dirname, dataFile09),
        expectedResult: expectedResult09,
      },
      test10: {
        wb: path.join(__dirname, dataFile10),
        expectedResult: expectedResult10,
      },
      test20: {
        skip: true,
        wb: path.join(__dirname, pwv),
        expectedResult: "none",
      },
      test21: {
        only: true,
        wb: path.join(__dirname, riverstar),
        expectedResult: "none",
      },
    },
  },
};

const tester = new Tester(testSets);
tester.run();
