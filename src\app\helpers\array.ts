export default {
  removeNullsFromBeginningOfArray(inputArray: any[]): any[] {
    const outputArray = [];
    inputArray.forEach((elem) => {
      if (this.isArray(elem)) {
        outputArray.push(this.removeNullsFromBeginningOfArray(elem));
      } else {
        if (elem !== null || outputArray.length > 0) {
          outputArray.push(elem);
        }
      }
    });

    return outputArray;
  },

  sortObjectArrayByUniquePropertyNameDotCount(
    objectArray: {[key: string]: any}[],
  ): {[key: string]: any}[] {
    return objectArray.sort(function (objectA, objectB) {
      const dotCountA = (Object.keys(objectA)[0].match(/\./g) || []).length;
      const dotCountB = (Object.keys(objectB)[0].match(/\./g) || []).length;

      if (!dotCountA && dotCountB) return 1;
      if (dotCountA && !dotCountB) return -1;
      if (!dotCountA && !dotCountB) return 0;
      return dotCountA - dotCountB;
    });
  },
};
