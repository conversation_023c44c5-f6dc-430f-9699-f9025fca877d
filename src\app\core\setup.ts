import Screenshot from "@/app/core/screenshot";
import cypressHelper from "@/app/helpers/cypress";

export default {
  before() {
    Cypress.sdt.config.writeResultsToFile =
      Cypress.sdt.config.writeResultsToFile ?? "yes";
    if (Cypress.sdt.config.writeResultsToFile.toLowerCase().trim() === "yes") {
      Cypress.sdt.report.setFolder();
    }
  },
  beforeEach() {
    Cypress.sdt.resetDomain();
  },
  afterEach() {
    if (
      Cypress.sdt.current.step.hasError &&
      Cypress.sdt.config.takeScreenshotOnError?.toLowerCase().trim() === "yes"
    ) {
      Screenshot.takeScreenshot();
    }
  },
  after() {
    Cypress.sdt.results.testsWithError =
      Cypress.sdt.results.executedTests.filter((test) =>
        test["steps"].find((step) => step.hasError)
      );
    cypressHelper.clog(null, Cypress.sdt);
    Cypress.sdt.config.writeResultsToFile =
      Cypress.sdt.config.writeResultsToFile ?? "yes";
    if (Cypress.sdt.config.writeResultsToFile.toLowerCase().trim() === "yes") {
      Cypress.sdt.report.write();
    }
  },
};
