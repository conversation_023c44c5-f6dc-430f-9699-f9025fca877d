import writeResults from "@/app/plugins/writeResults/writeResults";

const data = {
  "sdtFilePath": "C:\\Data\\rs-projects\\sdt\\src\\run\\riverstar\\riverstar.xlsx",
  "tests": [
      {
          "test": "1",
          "feature": "Home Page",
          "useCase": "Check Home Page Contents",
          "steps": [
              {
                  "simpleValues": [],
                  "namedValues": [],
                  "rowNumberLabel": "",
                  "do": "x",
                  "action": "Start Test",
                  "label": "Start Test",
                  "hasError": false,
                  "targetObject": {
                      "label": "",
                      "isPartial": false,
                      "isVisible": true,
                      "elements": []
                  }
              },
              {
                  "simpleValues": [],
                  "namedValues": [],
                  "rowNumberLabel": "9",
                  "do": "x",
                  "action": "Start Script - Login",
                  "rowNumber": "9",
                  "label": "(9) Start Script - Login",
                  "hasError": false,
                  "targetObject": {
                      "label": "",
                      "isPartial": false,
                      "isVisible": true,
                      "elements": []
                  }
              },
              {
                  "simpleValues": [],
                  "namedValues": [],
                  "rowNumberLabel": "9.5",
                  "do": "x",
                  "action": "Login Admin",
                  "rowNumber": "9.5",
                  "label": "(9.5) Login Admin",
                  "hasError": false,
                  "targetObject": {
                      "label": "",
                      "isPartial": false,
                      "isVisible": true,
                      "elements": []
                  }
              },
              {
                  "simpleValues": [],
                  "namedValues": [],
                  "rowNumberLabel": "9.6",
                  "do": "x",
                  "action": "Wait",
                  "rowNumber": "9.6",
                  "label": "(9.6) Wait",
                  "hasError": false,
                  "targetObject": {
                      "label": "",
                      "isPartial": false,
                      "isVisible": true,
                      "elements": []
                  }
              },
              {
                  "simpleValues": [
                      "Home"
                  ],
                  "namedValues": [],
                  "rowNumberLabel": "9.7",
                  "do": "x",
                  "action": "Open Page",
                  "values": "Home",
                  "rowNumber": "9.7",
                  "label": "(9.7) Open Page\nValues: \"Home\"",
                  "hasError": false,
                  "icon": {},
                  "targetObject": {
                      "label": "",
                      "isPartial": false,
                      "isVisible": true,
                      "elements": []
                  }
              },
              {
                  "simpleValues": [
                      "/desktop/app/introduction"
                  ],
                  "namedValues": [],
                  "rowNumberLabel": "9.8",
                  "do": "x",
                  "action": "Check Url",
                  "values": "/desktop/app/introduction",
                  "rowNumber": "9.8",
                  "label": "(9.8) Check Url\nValues: \"/desktop/app/introduction\"",
                  "hasError": false,
                  "icon": {},
                  "targetObject": {
                      "label": "",
                      "isPartial": false,
                      "isVisible": true,
                      "elements": []
                  }
              },
              {
                  "simpleValues": [],
                  "namedValues": [],
                  "rowNumberLabel": "9",
                  "do": "x",
                  "action": "End Script - Login",
                  "rowNumber": "9",
                  "label": "(9) End Script - Login",
                  "hasError": false,
                  "targetObject": {
                      "label": "",
                      "isPartial": false,
                      "isVisible": true,
                      "elements": []
                  }
              },
              {
                  "simpleValues": [
                      "Home"
                  ],
                  "namedValues": [],
                  "rowNumberLabel": "10",
                  "do": "x",
                  "action": "Check",
                  "target": "«Breadcrumb»",
                  "values": "Home",
                  "rowNumber": "10",
                  "label": "(10) Check\nTarget: «Breadcrumb»\nValues: \"Home\"",
                  "hasError": false,
                  "icon": {},
                  "targetObject": {
                      "label": "«Breadcrumb»",
                      "isPartial": false,
                      "isVisible": true,
                      "elements": [
                          {
                              "locatorName": "byContent",
                              "root": "body",
                              "label": "{Breadcrumb}",
                              "isVisible": true,
                              "isLast": true,
                              "name": "{Breadcrumb}",
                              "key": "Breadcrumb",
                              "selector": "desktop-breadcrumb:visible",
                              "content": ""
                          }
                      ],
                      "lastElement": {
                          "locatorName": "byContent",
                          "root": "body",
                          "label": "{Breadcrumb}",
                          "isVisible": true,
                          "isLast": true,
                          "name": "{Breadcrumb}",
                          "key": "Breadcrumb",
                          "selector": "desktop-breadcrumb:visible",
                          "content": ""
                      }
                  }
              },
              {
                  "simpleValues": [],
                  "namedValues": [],
                  "rowNumberLabel": "",
                  "do": "x",
                  "action": "End Test",
                  "label": "End Test",
                  "hasError": false,
                  "targetObject": {
                      "label": "",
                      "isPartial": false,
                      "isVisible": true,
                      "elements": []
                  }
              }
          ],
          "sheet": "Home Page",
          "runFlag": "y"
      }
  ],
  "outputFolderName": "C:\\Data\\rs-projects\\sdt\\src\\run\\riverstar\\results/2025-06-24T18.33.06",
  "outputFileName": "2025-06-24T18.33.06.xlsx"
}

writeResults(data);
