import Domain from "@/app/core/domain";
import Tester from "@/tests/node/tester";

const testSets = {
  setEntity: {
    getResult: function () {
      const domainData = { ...this.tables };
      const domain = new Domain(domainData);
      Object.values(domain.entities).map((value) => {
        if (typeof value === "object") delete value["token"];
      });
      domain.setEntity(this.keyPath, this.value);
      return {
        domainData: domain.entities,
      };
    },
    tests: {
      test01: {
        keyPath: "Entity1.Value",
        value: "abc",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: {
          domainData: {
            Entity1: { Id: "Entity1", Value: "abc" },
            currentEntityId: "Entity1",
          },
        },
      },
      test10: {
        keyPath: "Entity1.FieldA",
        value: "abc",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: {
          domainData: {
            Entity1: { Id: "Entity1", Value: "value1", FieldA: "abc" },
            currentEntityId: "Entity1",
          },
        },
      },
      test11: {
        keyPath: "Entity1.FieldA.FieldB",
        value: "abc",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: {
          domainData: {
            Entity1: {
              Id: "Entity1",
              Value: "value1",
              FieldA: { FieldB: "abc" },
            },
            currentEntityId: "Entity1",
          },
        },
      },
      test20: {
        keyPath: "Entity2",
        value: "abc",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: {
          domainData: {
            Entity1: { Id: "Entity1", Value: "value1" },
            Entity2: "abc",
            currentEntityId: "Entity2",
          },
        },
      },
    },
  },
  getEntityFieldValue: {
    only: true,
    getResult: function () {
      const domainData = { ...this.tables };
      const domain = new Domain(domainData);
      Object.values(domain.entities).map((value) => {
        if (typeof value === "object") delete value["token"];
      });
      return domain.getEntityFieldValue(this.keyPath);
    },
    tests: {
      test01: {
        keyPath: "[Entity1.Value]",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: "value1",
      },
      test02: {
        keyPath: "[Entity1]",
        tables: {
          Entity1: { Id: "Entity1", Value: "value1" },
        },
        expectedResult: { Id: "Entity1", Value: "value1" },
      },
      test03: {
        keyPath: "[User1][Office][State]",
        tables: {
          User1: { Id: "User1", Office: "[Office1]" },
          Office1: { Id: "Office1", State: "NY" },
        },
        expectedResult: "NY",
      },
      test04: {
        keyPath: "[User1][Office][State]",
        tables: {
          User1: { Id: "User1", Office: { State: "NY" } },
        },
        expectedResult: "NY",
      },
      test05: {
        keyPath: "[Office][State]",
        tables: {
          User1: { Id: "User1", Office: "[Office1]" },
          Office1: { Id: "Office1", State: "NY" },
          currentEntityId: "User1",
        },
        expectedResult: "NY",
      },
    },
  },
};

const tester = new Tester(testSets);
tester.run();
