import estensionTaskHandler from "./handlers/taskHandler";
import extensionActions from "./core/actions";
import extensionApiHandler from "./handlers/apiHandler";
import extensionIcons from "./core/icons";
import extensionPrimitives from "./core/primitives";
import extensionScheduleHandler from "./handlers/scheduleHandler";
import extensionSetup from "./core/setup";
import extensionStepperHandler from "./handlers/stepperHandler";
import h from "@/app/helpers/all";
import riverstarSdt from "../../.organization/sdt";

export default {
  setup: extensionSetup,
  config: riverstarSdt.config,
  icons: h.mergeObjects(riverstarSdt.icons, extensionIcons),
  actions: h.mergeObjects(riverstarSdt.actions, extensionActions),
  primitives: h.mergeObjects(riverstarSdt.primitives, extensionPrimitives),
  apiHandler: h.mergeObjects(riverstarSdt.apiHandler, extensionApiHandler),
  taskHandler: estension<PERSON><PERSON><PERSON><PERSON><PERSON>,
  stepperHandler: extensionStep<PERSON><PERSON><PERSON><PERSON>,
  scheduleHandler: extensionScheduleHandler,
} as const;
