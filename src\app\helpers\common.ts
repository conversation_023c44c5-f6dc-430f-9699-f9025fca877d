import https from "https";

export default {
  isDefined<T>(value: T | null | undefined): value is T {
    return value !== null && value !== undefined;
  },

  isString(value): value is string {
    return value && typeof value === "string";
  },

  isObject(value: any): value is object {
    return (
      typeof value === "object" &&
      typeof value !== "function" &&
      value !== null &&
      !Array.isArray(value)
    );
  },

  isArray(val): val is any[] {
    return val && Array.isArray(val);
  },

  isNotNullNorEmpty(value: any): boolean {
    return ["[]", "{}", "null"].indexOf(JSON.stringify(value)) === -1;
  },

  isEmptyObjectOrArray<T>(object: T): boolean {
    if (typeof object !== "object" || object === null) return false;
    return Object.keys(object).length === 0;
  },

  isEmpty(value: unknown): boolean {
    return (
      value === undefined ||
      value === "" ||
      value === null ||
      this.isEmptyObjectOrArray(value)
    );
  },

  colorCode: {
    red: "rgb(255, 0, 0)",
    "dark-washed-red": "rgb(244, 67, 54)",
    pink: "rgb(255, 192, 203)",
    orange: "rgb(255, 165, 0)",
    "orange-red": "rgb(241, 83, 62)",
    yellow: "rgb(255, 255, 0)",
    purple: "rgb(128, 0, 128)",
    green: "rgb(0, 128, 0)",
    cyan: "rgb(0, 255, 255)",
    blue: "rgb(0, 0, 255)",
    maroon: "rgb(128, 0, 0)",
    white: "rgb(255, 255, 255)",
    gray: "rgb(128, 128, 128)",
  },

  getFormattedPhoneNumber(phoneNumber: string, countryCode = 1): string {
    const phoneNumberPart1 = phoneNumber.substring(0, 3);
    const phoneNumberPart2 = phoneNumber.substring(3, 6);
    const phoneNumberPart3 = phoneNumber.substring(6);
    return `+${countryCode}(${phoneNumberPart1}) ${phoneNumberPart2}-${phoneNumberPart3}`;
  },

  getFormattedPhoneNumberWithoutCountryCode(phoneNumber: string): string {
    const phoneNumberPart1 = phoneNumber.substring(0, 3);
    const phoneNumberPart2 = phoneNumber.substring(3, 6);
    const phoneNumberPart3 = phoneNumber.substring(6);
    return `(${phoneNumberPart1}) ${phoneNumberPart2}-${phoneNumberPart3}`;
  },

  async httpsRequest(options: https.RequestOptions, body?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = https.request(options, (response) => {
        let responseData = "";
        
        response.on("data", (chunk) => {
          responseData += chunk;
        });

        response.on("end", () => {
          try {
            const parsedData = JSON.parse(responseData);
            if (response.statusCode && response.statusCode >= 400) {
              reject(new Error(`HTTP Error ${response.statusCode}: ${parsedData}`));
            } else {
              resolve(parsedData);
            }
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error}`));
          }
        });
      });

      request.on("error", (error) => {
        reject(error);
      });

      if (body) {
        request.write(body);
      }
      request.end();
    });
  },
};
