import Step from "@/app/core/step";
import h from "@/app/helpers/all";
import path from "path";

export default class Report {
  constructor(private config) {}

  setFolder() {
    const testDateTime = h.getLocalDateAndTime();
    this.config.resultsFolderName = path.join(
      this.config.resultsFolderPath,
      testDateTime
    );
    this.config.resultsFileName = `${testDateTime}.xlsx`;
    cy.task("createFolder", this.config.resultsFolderPath);
    cy.task("createFolder", this.config.resultsFolderName);
  }

  write() {
    const tests = Cypress.sdt.results.executedTests.map((test) => {
      test["steps"] = test["steps"].filter((step) => step instanceof Step);
      return test;
    });
    const writeResultsData = {
      sdtFilePath: this.config.sdtFilePath,
      tests,
      outputFolderName: this.config.resultsFolderName,
      outputFileName: this.config.resultsFileName,
    };
    cy.task("moveFiles", {
      sourceFolder: this.config.resultsScreenshotsTempFolder,
      destinationFolder: this.config.resultsFolderName,
    });
    cy.task("deleteFolder", this.config.resultsScreenshotsTempFolder);
    cy.task("writeResults", writeResultsData);
    const videoFilePath = "cypress/videos/runSdt.mp4";
    const destinationFilePath = path.join(
      this.config.resultsFolderName,
      "video.mp4"
    );
    cy.task("moveFile", { sourceFilePath: videoFilePath, destinationFilePath });
  }
}
