import Entry from "./entry";
import h from "@/app/helpers/all";

interface Entity {
  token?: string;
  [key: string]: any;
}

export default class Domain {
  public readonly entities: Map<string, Entity>;
  private currentEntityId: string | null;
  public data: Record<string, any> = {};

  constructor(initialData: Record<string, any>) {
    this.entities = new Map();
    this.currentEntityId = null;

    Object.entries(initialData).forEach(([key, value]) => {
      this.entities.set(key, {
        ...value,
        token: value.token || this.generateToken(),
      });
    });
  }

  private generateToken(): string {
    return h.randomNumber();
  }

  isEntity(key: string): boolean {
    return this.entities.has(key);
  }

  setEntity(fieldPath: string, fieldValue: any): void {
    const keys = fieldPath
      .replace(/^\[/g, "")
      .replace(/\]$/g, "")
      .replace(/\] *\[/g, ".")
      .split(".");
    const entityId = keys[0];

    const entry = new Entry(fieldValue);
    const value = entry.expandedEntry;
    const entity = this.getOrCreateEntity(entityId);
    this.updateEntityWithKeys(entity, keys.slice(1), value);
    this.setCurrentEntity(entityId);
  }

  private getOrCreateEntity(entityId: string): Entity {
    if (!this.entities.has(entityId)) {
      this.entities.set(entityId, { token: this.generateToken() });
    }
    return this.entities.get(entityId)!;
  }

  private updateEntityWithKeys(
    entity: Entity,
    keys: string[],
    value: any
  ): void {
    let current = entity;
    for (let i = 0; i < keys.length - 1; i++) {
      current[keys[i]] = current[keys[i]] || {};
      current = current[keys[i]];
    }

    const lastKey = keys[keys.length - 1];
    if (h.isObject(value)) {
      current[lastKey] = value;
    } else {
      current[lastKey] = value.replace("token", entity.token || "");
    }
  }

  getEntityFieldValue(reference: any): any {
    const keys = reference
      .replace(/^\[/g, "")
      .replace(/\]$/g, "")
      .replace(/\] *\[/g, ".")
      .split(".");
    const value = keys.reduce((result, part) => {
      let fieldValue = h.getObjectFieldValueWithNormalizedKey(result, part);
      const entry = new Entry(fieldValue);
      fieldValue = entry.expandedEntry;
      return fieldValue;
    }, Object.fromEntries(this.entities));
    return value;
  }

  getCurrentEntityId(): string | null {
    return this.currentEntityId;
  }

  resetCurrentEntityId(): void {
    this.currentEntityId = null;
  }

  getCurrentEntity(): Entity | null {
    if (!this.currentEntityId) return null;
    return this.entities.get(this.currentEntityId) || null;
  }

  getCurrentEntityFieldValue(entityField: string): any {
    const currentEntity = this.getCurrentEntity();
    if (!currentEntity) return "";
    return h.getObjectFieldValueWithNormalizedKey(currentEntity, entityField);
  }

  setCurrentEntity(entityId: string): void {
    this.getOrCreateEntity(entityId);
    this.currentEntityId = entityId;
  }

  setCurrentEntityToken(): void {
    const currentEntity = this.getCurrentEntity();
    if (currentEntity && !currentEntity.token) {
      currentEntity.token = this.generateToken();
    }
  }

  getCurrentEntityToken(): string | null {
    const currentEntity = this.getCurrentEntity();
    return currentEntity?.token || null;
  }

  checkCurrentEntityContainsField(entityField: string): boolean {
    const currentEntity = this.getCurrentEntity();
    if (!currentEntity) return false;
    return !!h.getObjectFieldValueWithNormalizedKey(currentEntity, entityField);
  }

  getAllEntities(): Record<string, Entity> {
    return Object.fromEntries(this.entities);
  }
}
