{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug with ts-node", "runtimeArgs": ["-r", "ts-node/register"], "args": ["${relativeFile}"], "cwd": "${workspaceFolder}", "sourceMaps": true}, {"type": "node", "request": "launch", "name": "Debug Cypress Task", "program": "${workspaceFolder}/node_modules/.bin/cypress", "args": ["run"], "cwd": "${workspaceFolder}", "env": {"CYPRESS_TASK_DEBUG": "1"}, "skipFiles": ["<node_internals>/**"]}]}