[{"_id": {"$oid": "630ff5cf2186e4a1fe3800cb"}, "templateName": "sms-cbmm-confirmInvestigationLetter", "templateHtml": "Consumers Energy Mixed Meters\n\nOur records indicate a possible mixed meter situation has been reported at your address listed below:\n\n{serviceAddress} \n\nWe have attempted numerous times to inform you we are required to conduct a meter investigation. The investigation is scheduled for: {data.investigationDate:dt:MM/DD/YYYY} and your assistance is greatly appreciated. Our service worker will kindly need to be granted permission to access both your exterior meter as well as area(s) inside your {data.accountType:lowerCase} to perform the investigation.\n\nPlease accept our sincere apologies for any inconvenience this may cause. For any questions, please call our Customer Service team at 1-800- 477-5050, extension {currentUser.phone:last-chars:4} or contact me directly at {currentUser.phone:phone} or {currentUser.email}.\n\nWe appreciate your attention to this important matter.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-223044", "updatedAt": {"$date": "2022-12-12T20:22:14.727Z"}, "createdAt": {"$date": "2022-08-31T23:59:11.806Z"}, "__v": 0}, {"_id": {"$oid": "630ff39d2186e4a1fe3800bc"}, "templateName": "email-cbmm-confirmInvestigationLetter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div><span style=\"background-color: transparent;\"><font face=\"Arial\">Our records indicate a possible mixed meter situation has been reported at the above service address. We have attempted numerous times to inform you we are required to conduct a meter investigation. The investigation is scheduled for: {data.investigationDate:dt:MM/DD/YYYY}&nbsp;and your assistance is greatly appreciated. Our service worker will kindly need to be granted permission to access both your exterior meter as well as area(s) inside your&nbsp;</font></span><span style=\"background-color: transparent;\">{data.accountType</span><span style=\"background-color: transparent;\">:lowerCase</span><span style=\"background-color: transparent;\">}&nbsp;</span><span style=\"background-color: transparent;\">to perform the investigation.</span></div><div><br></div><div><span style=\"background-color: transparent;\">Please accept our sincere apologies for any inconvenience this may cause. For any questions, please call our Customer Service team at 1-800- 477-5050, extension&nbsp;</span><span style=\"background-color: transparent;\">{currentUser.phone:last-chars:4} or contact me directly at&nbsp;{currentUser.phone:phone}</span><span style=\"background-color: transparent;\">&nbsp;or {currentUser.email}.</span><br></div><div><span style=\"background-color: transparent;\"><br></span></div><div><div><br></div><div><div>We appreciate your attention to this important matter.</div><div><br></div><div>&nbsp;</div><div><span style=\"background-color: transparent;\">Sincerely,</span><br></div></div></div></div><div><br></div><div><br></div><div><div>{currentUser.firstName} {currentUser.lastName}</div><div><br></div><div><div><div><div>Mixed Meters</div><div><br></div><div>Please do not reply to this automated message.</div></div></div></div></div><div><br></div></div>", "isActive": true, "version": "********-220840", "updatedAt": {"$date": "2022-12-12T20:22:14.747Z"}, "createdAt": {"$date": "2022-08-31T23:49:49.748Z"}, "__v": 0}, {"_id": {"$oid": "630fd8bd10defd49072baf76"}, "templateName": "sms-cbmm-creditLetter", "templateHtml": "Consumers Energy Mixed Meters\n\nDuring our recent mixed meter investigation, we discovered you were billed on the incorrect {data.creditServiceType} meter at your address listed below:\n\n{serviceAddress}\n\nWe have corrected our records and applied a credit of ${data.creditAmount:de} to your {data.addressType:lowerCase} address.\n\n\nPlease accept our sincere apologies for any inconvenience this may have caused. For any questions, please call our Customer Service team at **************, extension {currentUser.phone:last-chars:4} or contact me directly at {currentUser.phone:phone} or {currentUser.email}.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-223216", "updatedAt": {"$date": "2022-12-12T20:22:14.755Z"}, "createdAt": {"$date": "2022-08-31T21:55:09.890Z"}, "__v": 0}, {"_id": {"$oid": "630fce7e10defd49072baf43"}, "templateName": "email-cbmm-creditLetter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div><span style=\"background-color: transparent;\"><font face=\"Arial\">During our recent mixed meter investigation, we discovered you were billed on the incorrect&nbsp;</font></span>{data.creditServiceType}<span style=\"background-color: transparent;\">&nbsp;meter at your address listed above.</span></div><div><br></div><div>We have corrected our records and applied a credit of $<span style=\"background-color: transparent;\">{data.creditAmount:de}&nbsp;</span><span style=\"background-color: transparent;\">&nbsp;to your&nbsp;</span><span style=\"background-color: transparent;\">{data.addressType:lowerCase} address.</span></div><div><span style=\"background-color: transparent;\"><br></span></div><div><span style=\"background-color: transparent;\">Please accept our sincere apologies for any inconvenience this may have caused. For any questions, please call our Customer Service team at **************, extension&nbsp;</span><span style=\"background-color: transparent;\">{currentUser.phone:last-chars:4} or contact me directly at {currentUser.phone:phone}</span><span style=\"background-color: transparent;\">&nbsp;or {currentUser.email}.</span><br></div><div><span style=\"background-color: transparent;\"><br></span></div><div><div><br></div><div>Sincerely,</div></div></div><div><br></div><div><div>{currentUser.firstName} {currentUser.lastName}</div><div><br></div><div><div><div><div>Mixed Meters</div><div><br></div><div>Please do not reply to this automated message.</div></div></div></div></div><div><br></div></div>", "isActive": true, "version": "********-210201", "updatedAt": {"$date": "2022-12-12T20:22:14.759Z"}, "createdAt": {"$date": "2022-08-31T21:11:26.886Z"}, "__v": 0}, {"_id": {"$oid": "630e95164f6d97e08f9ceaa5"}, "templateName": "sms-cbmm-nonRegisteredLetter", "templateHtml": "Consumers Energy Customer Billing\n\nWe recently reviewed your energy account at {serviceAddress} and noticed that your meter has not been registering {data.cbServiceType} usage.  Based on your previous usage, we believe the meter may not be working properly.\n\nOne of our knowledgeable service representatives will check your current meter soon and correct and problems.  Your next energy meter bill will be adjusted to include your previous usage.  Please accept our apology for any inconvenience this may cause you.\n\nWe appreciate having you as our valued customer.  As your energy supplier, we’re working hard every day to provide you with safe, reliable, and affordable energy service.\n\nIf you have any questions or concerns about your account, please call our customer service center toll free at (800) 477-5050.  One of our customer service representatives will be happy to help you.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-223521", "updatedAt": {"$date": "2022-12-12T20:22:14.764Z"}, "createdAt": {"$date": "2022-08-30T22:54:14.213Z"}, "__v": 0}, {"_id": {"$oid": "630e8fa49f5ea48761352890"}, "templateName": "email-cbmm-nonRegisteredLetter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div>We recently reviewed your energy account at the above address and noticed that your meter has not been registering {data.cbServiceType}&nbsp;<span style=\"background-color: transparent;\">usage.&nbsp; Based on your previous usage, we believe the meter may not be working properly.</span></div><div><br></div><div>One of our knowledgeable service representatives will check your current meter soon and correct and problems.&nbsp; Your next energy meter bill will be adjusted to include your previous usage.&nbsp; Please accept our apology for any inconvenience this may cause you.</div><div><br></div><div>We appreciate having you as our valued customer.&nbsp; As your energy supplier, we’re working hard every day to provide you with safe, reliable, and affordable energy service.</div><div><br></div><div>If you have any questions or concerns about your account, please call our customer service center toll free at (800) 477-5050.&nbsp; One of our customer service representatives will be happy to help you.</div><div><br></div><div>&nbsp;</div><div><br></div><div>Customer Billing</div><div><br></div><div><a href=\"\\&quot;https://www.consumersenergy.com\" target=\"\\&quot;_blank\\&quot;\">www.consumersenergy.com</a></div><div><br></div><div>Please do not reply to this automated message.</div></div><div><br></div></div>", "isActive": true, "version": "********-232417", "updatedAt": {"$date": "2022-12-12T20:22:14.769Z"}, "createdAt": {"$date": "2022-08-30T22:31:00.518Z"}, "__v": 0}, {"_id": {"$oid": "630e8d899f5ea48761352882"}, "templateName": "sms-cbmm-accessLetter", "templateHtml": "Consumers Energy Customer Billing\n\nIn reviewing your energy account, recent meter readings indicate no {data.cbServiceType} usage.\n\nPlease call our customer service center toll free at (800) 477-5050 within the next few days so we can arrange to have one of our service representatives visit your home and exchange the meter when it is convenient for you.\n\nUntil we are able to install a new meter, we will estimate your energy bill based on your previous usage.\n\nPlease accept our apology for any inconvenience this may cause.  We value having you as our customer and providing you with safe, reliable, and affordable energy service.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-223612", "updatedAt": {"$date": "2022-12-12T20:22:14.773Z"}, "createdAt": {"$date": "2022-08-30T22:22:01.212Z"}, "__v": 0}, {"_id": {"$oid": "630e8d259f5ea4876135287c"}, "templateName": "email-cbmm-accessLetter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div>In reviewing your energy account, recent meter readings indicate no&nbsp;{data.cbServiceType}&nbsp;<span style=\"background-color: transparent;\">usage.</span></div><div><br></div><div>Please call our customer service center toll free at (800) 477-5050 within the next few days so we can arrange to have one of our service representatives visit your home and exchange the meter when it is convenient for you.</div><div><br></div><div>Until we are able to install a new meter, we will estimate your energy bill based on your previous usage.</div><div><br></div><div>Please accept our apology for any inconvenience this may cause.&nbsp; We value having you as our customer and providing you with safe, reliable, and affordable energy service.</div><div><br></div><div>&nbsp;</div><div><br></div><div>Customer Billing</div><div><br></div><div><a href=\"\\&quot;https://www.consumersenergy.com\" target=\"\\&quot;_blank\\&quot;\">www.consumersenergy.com</a></div><div><br></div><div>Please do not reply to this automated message.</div></div><div><br></div><div><br></div></div>", "isActive": true, "version": "********-232442", "updatedAt": {"$date": "2022-12-12T20:22:14.776Z"}, "createdAt": {"$date": "2022-08-30T22:20:21.663Z"}, "__v": 0}, {"_id": {"$oid": "630e32c5ffcd880f91430a3e"}, "templateName": "sms-cbmm-completedLetter", "templateHtml": "Consumers Energy Mixed Meters\n\nOur recent mixed meter investigation found that you have been billed on the wrong {data.mmServiceType} meter. We have corrected our records to ensure that you are billed for the proper meter.\n\nAfter receiving your bill(s), if you have any questions, please call Customer Service at 1-800- 477-5050 ext. {currentUser.phone:last-chars:4} or contact me directly at {currentUser.phone:phone}.  If you prefer, you can also email me at {currentUser.email}.\n\nPlease accept our apologies for any inconvenience this may cause.\n\nThank you,\n\n{currentUser.firstName} {currentUser.lastName}\n\nMixed Meters\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-223703", "updatedAt": {"$date": "2022-12-12T20:22:14.782Z"}, "createdAt": {"$date": "2022-08-30T15:54:45.869Z"}, "__v": 0}, {"_id": {"$oid": "630e31f0ffcd880f91430a36"}, "templateName": "email-cbmm-completedLetter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div>Our recent mixed meter investigation found that you have been billed on the wrong {data.mmServiceType} meter. We have corrected our records to ensure that you are billed for the proper meter.</div><div><br></div><div><span style=\"background-color: transparent;\">After receiving your bill(s), if you have any questions, please call Customer Service at 1-800- 477-5050 ext.</span><span style=\"background-color: transparent;\">&nbsp;{currentUser.phone:last-chars:4} or contact me directly at&nbsp;{currentUser.phone:phone}.&nbsp;&nbsp;</span><span style=\"background-color: transparent;\">If you prefer, you can also email me at {currentUser.email}.</span><br></div><div><span style=\"background-color: transparent;\"><br></span></div><div><div>Please accept our apologies for any inconvenience this may cause.</div><div><br></div><div>Thank you,</div></div></div><div><br></div><div><br></div><div><div>{currentUser.firstName} {currentUser.lastName}</div><div><br></div><div><div><div>Mixed Meters</div><div><br></div><div>Please do not reply to this automated message.</div></div></div></div><div><br></div><div><br></div></div>", "isActive": true, "version": "20220830-223203", "updatedAt": {"$date": "2022-12-12T20:22:14.786Z"}, "createdAt": {"$date": "2022-08-30T15:51:12.185Z"}, "__v": 0}, {"_id": {"$oid": "630d152a3289e44bf458f04e"}, "templateName": "sms-cbmm-closeoutLetter", "templateHtml": "Consumers Energy Mixed Meters\n\nThis letter is regarding your concern about a mixed meter at {serviceAddress}.  I have been unable to reach you by phone.  Please call us at your earliest convenience so we can schedule a day when you can meet us to verify the meters at each address.\n\nYou can call me at ************** ext {currentUser.phone:last-chars:4} or directly at {currentUser.phone:phone}.  If you prefer, you can also email me at {currentUser.email}.\n\nNOTE: If we do not hear from you within 30 days we will consider this request closed. If in the future you would like us to revisit the mixed meter concern please contact our customer service at ************. \n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-223750", "updatedAt": {"$date": "2022-12-12T20:22:14.790Z"}, "createdAt": {"$date": "2022-08-29T19:36:10.690Z"}, "__v": 0}, {"_id": {"$oid": "630d11943289e44bf458f039"}, "templateName": "email-cbmm-closeoutLetter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div>This letter is regarding your concern about a mixed meter at the above address.&nbsp; I have been unable to reach you by phone.&nbsp; Please call us at your earliest convenience so we can schedule a day when you can meet us to verify the meters at each address.</div><div><br></div><div>You can call me at ************** ext {currentUser.phone:last-chars:4}&nbsp;or directly at {currentUser.phone:phone}.&nbsp; If you prefer, you can also email me at&nbsp;<span style=\"background-color: transparent;\">{currentUser.email}.</span></div><div><br></div></div><div><div>NOTE: If we do not hear from you within 30 days we will consider this request closed. If in the future you would like us to revisit the mixed meter concern please contact our customer service at ************.&nbsp;</div><div><br></div><div>&nbsp;</div><div><br></div><div>Thank you,</div><div><br></div><div>Mixed Meters</div><div><br></div><div>Please do not reply to this automated message.</div></div><div><br></div><div><br></div><div><br></div></div>", "isActive": true, "version": "20220916-191324", "updatedAt": {"$date": "2022-12-12T20:22:14.796Z"}, "createdAt": {"$date": "2022-08-29T19:20:52.175Z"}, "__v": 0}, {"_id": {"$oid": "63080665df2bc3966fbc4f5a"}, "templateName": "sms-cbmm-freeFormLetter", "templateHtml": "Consumers Energy\n\n{data.letterText}\n\n\nPlease do not reply to this automated message.\n", "isActive": true, "version": "********-223809", "updatedAt": {"$date": "2022-12-12T20:22:14.800Z"}, "createdAt": {"$date": "2022-08-25T23:31:49.008Z"}, "__v": 0}, {"_id": {"$oid": "63080630df2bc3966fbc4f54"}, "templateName": "email-cbmm-freeFormLetter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div><font face=\"Arial\">{data.letterText</font><span style=\"background-color: transparent;\"><font face=\"Arial\">:to-html</font></span><span style=\"font-family: Arial; background-color: transparent;\">}</span></div></div><div><font face=\"Arial\"><br></font></div><div><div><div><br></div><div><div>Sincerely,</div><div><br></div><div>Consumers Energy</div><div><br></div><div><a href=\"\\&quot;https://www.consumersenergy.com\" target=\"\\&quot;_blank\\&quot;\">www.consumersenergy.com</a></div><div><br></div><div>Please do not reply to this automated message.</div></div></div></div><div><br></div><div><br></div></div>", "isActive": true, "version": "********-221851", "updatedAt": {"$date": "2022-12-12T20:22:14.804Z"}, "createdAt": {"$date": "2022-08-25T23:30:56.411Z"}, "__v": 0}, {"_id": {"$oid": "6307fa859557aa3a234b4a98"}, "templateName": "sms-cbmm-callbackLetter", "templateHtml": "Consumers Energy Mixed Meters\n\nPlease call our office, at your earliest convenience, regarding your mixed meter concern at the above address.  We will need to schedule a day when you can meet us there to verify the meters at each address.\n\nI can be reached at ************** ext {currentUser.phone:last-chars:4} or directly at {currentUser.phone:phone}.  If you prefer, you can also email me at {currentUser.email}.\n\n{currentUser.firstName} {currentUser.lastName}\n\nPlease do not reply to this automated message.\n", "isActive": true, "version": "********-223834", "updatedAt": {"$date": "2022-12-12T20:22:14.808Z"}, "createdAt": {"$date": "2022-08-25T22:41:09.692Z"}, "__v": 0}, {"_id": {"$oid": "6307f7c99557aa3a234b4a88"}, "templateName": "email-cbmm-callbackLetter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div>Please call our office, at your earliest convenience, regarding your mixed meter concern at the above address.&nbsp; We will need to schedule a day when you can meet us there to verify the meters at each address.</div><div><br></div><div>I can be reached at ************** ext {currentUser.phone:last-chars:4}&nbsp;or directly at {currentUser.phone:phone}.&nbsp; If you prefer, you can also email me at&nbsp;<span style=\"background-color: transparent;\">{currentUser.email}.</span></div><div>Thank you,</div></div><div><br></div><div><br></div><div><div>{currentUser.firstName} {currentUser.lastName}</div><div><br></div><div><div>Mixed Meters</div><div><br></div><div>Please do not reply to this automated message.</div></div></div><div><br></div><div><br></div><div><br></div></div>", "isActive": true, "version": "20220825-222929", "updatedAt": {"$date": "2022-12-12T20:22:14.811Z"}, "createdAt": {"$date": "2022-08-25T22:29:29.453Z"}, "__v": 0}, {"_id": {"$oid": "63067dfa21bb70195be5abe9"}, "templateName": "sms-cbmm-multipleBills", "templateHtml": "Consumers Energy Customer Billing\n\nWe’ve corrected a system error delaying us from sending your monthly energy bill in a timely manner.\n\nYou will be receiving {data.numberOfBills:lowerCase} energy bills. Please pay only the bill with the latest billing period. We’ve extended the due date for your convenience. All other bills are included only for your review.\n\nWe apologize for any inconvenience this may have caused.  If you have questions, please visit our website at http://www.consumersenergy.com \n\nWe value you as our customer. It’s our pleasure to provide you with safe, reliable, affordable and sustainable energy.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-224014", "updatedAt": {"$date": "2022-12-12T20:22:14.814Z"}, "createdAt": {"$date": "2022-08-24T22:02:45.045Z"}, "__v": 0}, {"_id": {"$oid": "63067c0f21bb70195be5abdc"}, "templateName": "email-cbmm-multipleBills", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div>We’ve corrected a system error delaying us from sending your monthly energy bill in a timely manner.</div><div><br></div><div>You will be receiving {data.numberOfBills:lowerCase} energy bills. Please pay only the bill with the latest billing period. We’ve extended the due date for your convenience. All other bills are included only for your review.</div><div><br></div><div>We apologize for any inconvenience this may have caused.&nbsp; If you have questions, please visit our website at&nbsp;<a href=\"http://www.consumersenergy.com\" target=\"_blank\">http://www.consumersenergy.com</a>&nbsp;</div><div><br></div><div>We value you as our customer. It’s our pleasure to provide you with safe, reliable, affordable and sustainable energy.</div><div><br></div><div>Sincerely,</div><div><br></div><div>Consumers Energy Customer Billing</div><div><a href=\"http://www.consumersenergy.com\" target=\"_blank\" style=\"background-color: transparent;\">http://www.consumersenergy.com</a>&nbsp;</div><div><br></div><div>Please do not reply to this automated message.<br></div></div>", "isActive": true, "version": "********-205459", "updatedAt": {"$date": "2022-12-12T20:22:14.817Z"}, "createdAt": {"$date": "2022-08-24T22:02:45.045Z"}, "__v": 0}, {"_id": {"$oid": "630672bd21bb70195be5abac"}, "templateName": "sms-cbmm-catchUpBill", "templateHtml": "Consumers Energy Customer Billing\n\nOn {data.meterReadDate:dt:MM/DD/YYYY} we have obtained a meter read and we have made corrections to ensure you receive an accurate bill. If not received already, your corrected bill will arrive very soon.\n\nUntil recently, we have estimated your energy use. We understand how important it is to receive an accurate bill for the actual energy you use, and we apologize for any inconvenience this may have caused.\n\nYou are eligible to pay this bill over the number of months your energy use was estimated. Also, other emergency and ongoing aid may be available. Please contact Consumers Energy at ************ to explore these payment options.\n\nThanks for being our valued customer. It’s our pleasure to provide you with safe, reliable, affordable, sustainable energy.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-224136", "updatedAt": {"$date": "2022-12-12T20:22:14.821Z"}, "createdAt": {"$date": "2022-08-24T22:02:45.045Z"}, "__v": 0}, {"_id": {"$oid": "6306703221bb70195be5ab99"}, "templateName": "email-cbmm-catchUpBill", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},&nbsp;</font></b><br><br></div><div><div>On {data.meterReadDate:dt:MM/DD/YYYY} we have obtained a meter read and we have made corrections to ensure you receive an accurate bill. If not received already, your corrected bill will arrive very soon.&nbsp;</div><div><br></div><div>Until recently, we have estimated your energy use. We understand how important it is to receive an accurate bill for the actual energy you use, and we apologize for any inconvenience this may have caused.&nbsp;</div><div><br></div><div>You are eligible to pay this bill over the number of months your energy use was estimated. Also, other emergency and ongoing aid may be available. Please contact Consumers Energy at ************ to explore these payment options.&nbsp; &nbsp;&nbsp;</div><div><br></div><div>Thanks for being our valued customer. It’s our pleasure to provide you with safe, reliable, affordable, sustainable energy.</div><div><br></div><div>&nbsp;</div><div><br></div><div>Sincerely,</div><div><br></div><div>Customer Billing Department</div><div><br></div><div>Consumers Energy&nbsp;</div><div><br></div><div>Please do not reply to this automated message.<br></div></div><div><br></div>", "isActive": true, "version": "20220829-183755", "updatedAt": {"$date": "2022-12-12T20:22:14.828Z"}, "createdAt": {"$date": "2022-08-24T22:02:45.045Z"}, "__v": 0}, {"_id": {"$oid": "62ffc1fffc072caf65556b9e"}, "templateName": "sms-cbmm-delayInBillingWithDueDate", "templateHtml": "Consumers Energy Customer Billing\n\nWe have identified an issue with your energy bill, and we’re making adjustments to ensure your bill is accurate. Delivery will be delayed, and your adjusted bill with an extended due date should arrive on or about {data.billDeliveryDate:dt:MM/DD/YYYY}.\n\nIn our efforts to keep you informed, we apologize for any inconvenience this may cause. If you have questions once you receive your bill, please contact me directly at {currentUser.phone:phone}  or {currentUser.email}.\n\nWe value you as our customer. It’s our pleasure to provide you with safe, reliable, affordable and sustainable energy.\n\nPlease do not reply to this automated message.\n", "isActive": true, "version": "********-220542", "updatedAt": {"$date": "2022-12-12T20:22:14.833Z"}, "createdAt": {"$date": "2022-08-24T22:02:45.045Z"}, "__v": 0}, {"_id": {"$oid": "62ffc0f3fc072caf65556b95"}, "templateName": "email-cbmm-delayInBillingWithDueDate", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div>We have identified an issue with your energy bill, and we’re making adjustments to ensure your bill is accurate. Delivery will be delayed, and your adjusted bill with an extended due date should arrive on or about {data.billDeliveryDate:dt:MM/DD/YYYY}.</div><div><br></div><div>In our efforts to keep you informed, we apologize for any inconvenience this may cause. If you have questions once you receive your bill, please contact me directly at the phone number or email below.</div><div><br></div><div>We value you as our customer. It’s our pleasure to provide you with safe, reliable, affordable and sustainable energy.</div><div><br></div><div>&nbsp;</div><div><br></div><div>Sincerely,</div><div><br></div><div>&nbsp;</div><div><br></div><div><div>{currentUser.firstName} {currentUser.lastName}</div><div><br></div><div>{currentUser.phone:phone}&nbsp;</div><div><br></div><div>{currentUser.email}</div><div><br></div><div>Consumers Energy Customer Billing</div></div><div><br></div><div>Please do not reply to this automated message.<br></div><div><br></div><div><br></div><div><br></div></div></div>", "isActive": true, "version": "20220829-185148", "updatedAt": {"$date": "2022-12-12T20:22:14.836Z"}, "createdAt": {"$date": "2022-08-24T22:02:45.045Z"}, "__v": 0}, {"_id": {"$oid": "62ffb887fc072caf65556b6c"}, "templateName": "sms-cbmm-delayInBillingWODueDate", "templateHtml": "Consumers Energy Customer Billing\n\nWe have identified an issue with your energy bill, and we’re making adjustments to ensure your bill is accurate. Delivery will be delayed, and we will send your bill with an extended due date when adjustments are complete.\n\nIn our efforts to keep you informed, we apologize for any inconvenience this may cause.\n\nWe value you as our customer. It’s our pleasure to provide you with safe, reliable, affordable and sustainable energy.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-224349", "updatedAt": {"$date": "2022-12-12T20:22:14.839Z"}, "createdAt": {"$date": "2022-08-24T22:02:45.045Z"}, "__v": 0}, {"_id": {"$oid": "62ffb824fc072caf65556b65"}, "templateName": "email-cbmm-delayInBillingWODueDate", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div>We have identified an issue with your energy bill, and we’re making adjustments to ensure your bill is accurate. Delivery will be delayed, and we will send your bill with an extended due date when adjustments are complete.</div><div><br></div><div>In our efforts to keep you informed, we apologize for any inconvenience this may cause.</div><div><br></div><div>We value you as our customer. It’s our pleasure to provide you with safe, reliable, affordable and sustainable energy.</div><div><br></div><div>&nbsp;</div><div><br></div><div>Customer Billing Department</div><div><br></div><div>Consumers Energy</div><div><br></div><div><a href=\"\\&quot;https://www.consumersenergy.com&quot;\" target=\"\\&quot;_blank\\&quot;\">www.consumersenergy.com</a></div></div></div><div><br></div><div>Please do not reply to this automated message.<br></div>", "isActive": true, "version": "********-221400", "updatedAt": {"$date": "2022-12-12T20:22:14.842Z"}, "createdAt": {"$date": "2022-08-24T22:02:45.045Z"}, "__v": 0}, {"_id": {"$oid": "62fca72ecbfcd2998030081f"}, "templateName": "sms-rev-medical-letter-vpn", "templateHtml": "Need help with your bill?\n\nThere are a variety of programs to meet your needs. Dial 211 for details.", "isActive": true, "version": "20220817-083038", "updatedAt": {"$date": "2022-12-12T20:22:14.846Z"}, "createdAt": {"$date": "2022-08-22T07:27:14.427Z"}, "__v": 0}, {"_id": {"$oid": "62fca6e9cbfcd29980300819"}, "templateName": "sms-rev-medical-letter-hhc", "templateHtml": "Need help with your bill?\n\nYou may be eligible for a Home Heating Credit (HHC). Di<PERSON> 211 before Sept. 30 to learn more.", "isActive": true, "version": "20220817-082929", "updatedAt": {"$date": "2022-12-12T20:22:14.849Z"}, "createdAt": {"$date": "2022-08-22T07:27:14.427Z"}, "__v": 0}, {"_id": {"$oid": "62fca6d9cbfcd29980300814"}, "templateName": "sms-rev-medical-letter-ser", "templateHtml": "Need help with your bill?\n\nAssistance is available. Apply for State Emergency Relief (SER) at https://newmibridges.michigan.gov/.", "isActive": true, "version": "20230109-182724", "updatedAt": {"$date": "2023-02-06T16:51:20.046Z"}, "createdAt": {"$date": "2022-08-22T07:27:14.427Z"}, "__v": 0}, {"_id": {"$oid": "62fca6c2cbfcd2998030080e"}, "templateName": "sms-rev-medical-letter-care", "templateHtml": "Need help with your bill? \n\nHave you checked your email? Our CARE program can help pay off your past-due balance and offer you a budget-friendly payment amount.", "isActive": true, "version": "20220817-082850", "updatedAt": {"$date": "2022-12-12T20:22:14.855Z"}, "createdAt": {"$date": "2022-08-22T07:27:14.426Z"}, "__v": 0}, {"_id": {"$oid": "62fca6afcbfcd29980300809"}, "templateName": "sms-rev-medical-letter-ebbb", "templateHtml": "Need help with your bill?  \n\nIf your energy bill is beyond your budget, dial 211 or visit mi211.org.", "isActive": true, "version": "20220817-082831", "updatedAt": {"$date": "2022-12-12T20:22:14.858Z"}, "createdAt": {"$date": "2022-08-22T07:27:14.424Z"}, "__v": 0}, {"_id": {"$oid": "62f3d27388544b5e731fe1f6"}, "templateName": "sms-csr-installment-plan", "templateHtml": "Thank you for calling Consumers Energy to set up a payment arrangement. Your request has been completed. Click here to view your payment schedule:\n{url}", "isActive": true, "version": "20220810-154451", "updatedAt": {"$date": "2022-12-12T20:22:14.863Z"}, "createdAt": {"$date": "2022-08-11T16:50:09.497Z"}, "__v": 0}, {"_id": {"$oid": "62e2f6d07225453a8faee610"}, "templateName": "sms-frt-id-theft", "templateHtml": "Thank you for calling Consumers Energy to report a claim of identify theft. Please click the link below to continue with your request and view documentation required.\n\n{url}", "isActive": true, "version": "********-205128", "updatedAt": {"$date": "2022-12-12T20:22:14.867Z"}, "createdAt": {"$date": "2022-08-04T19:14:17.435Z"}, "__v": 0}, {"_id": {"$oid": "62e2f6c97225453a8faee60b"}, "templateName": "email-frt-id-theft", "templateHtml": "<div style=\"width: 960px;\">\n<table style=\"width: 960px;\">\n<tbody>\n  <tr>\n    <td>\n      <img src=\"app-api/images/stored/id-theft-header.png\"></td>\n    </tr>\n    <tr>\n      <td>\n        <p>\n        </p><div style=\"text-align: justify;margin-top: -70px;\">\n          <table style=\"width: 960px; text-align: start;\">\n            <tbody>\n              <tr>\n                <td style=\"width: 150px;\"><br></td>\n                <td><br></td></tr>\n            </tbody>\n          </table>\n          </div>\n          <p><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br></p>\n            <p>\n            </p><p>Thank you for calling Consumers Energy to report a claim of identify theft to dispute charges at the service address of <font color=\"#004993\">{serviceAddress}</font>.</p><p>To continue with your claim, please visit <a href=\"https://www.consumersenergy.com/community/safety/identity-theft\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.consumersenergy.com/community/safety/identity-theft</a>.</p><p>Before Consumers Energy can open an investigation of ID Theft, the following documentation will be required:</p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><font size=\"5\" color=\"#004993\">❶</font>&nbsp;<span style=\"background-color: transparent;\">A submitted and notarized Federal Trade Commission (FTC) Identity Theft Report Form, which can be obtained at <a href=\"https://www.identitytheft.gov\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.identitytheft.gov</a><br></span><font size=\"5\" color=\"#004993\">❷</font>&nbsp;<span style=\"background-color: transparent;\">Copy (front and back) of a valid government-issued photo-identification card. Examples: Current driver’s license, state identification card or passport<br></span><font size=\"5\" color=\"#004993\">❸</font>&nbsp;<span style=\"background-color: transparent;\">Proof of residency for the time-frame in dispute. Examples: Rental/lease agreements, utility bills, paystubs, insurance documents, tax forms, bank statements.<br></span><span style=\"color: rgb(0, 73, 147); font-size: x-large;\">❹&nbsp;</span><span style=\"background-color: transparent;\">Filed police report of Identity Theft. If you are not able to obtain a copy of the report, please provide report number, date and location claim was filed, and officer’s name and contact information</span></blockquote><p>Once all required documentation has been received, a Consumers Energy investigator will contact you within 2-3 business days to confirm receipt and provide further instructions if needed. Please note that an investigation time frame may vary depending on the possibility of criminal prosecution.</p><p><br></p><p>Sincerely,</p><p>Consumers Energy</p><p>Please do not reply to this automated email message</p>\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n            </div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "********-205121", "updatedAt": {"$date": "2022-12-12T20:22:14.870Z"}, "createdAt": {"$date": "2022-08-04T19:14:17.435Z"}, "__v": 0}, {"_id": {"$oid": "62e2ca5d49813f0095f09e29"}, "templateName": "sms-frt-facta-id", "templateHtml": "FACTA (ID validation for new move-in request)\nAccount Number: ending in {accountNumber:last-chars:4}\nService Address: {serviceAddress}\n\nThank you for contacting Consumers Energy\n{data.requestDocumentation:start-check-exists}\n{data.requestDocumentation:check} Request for documentation {data.requestDocumentation:end-check-exists}{data.requestId:start-check-exists}\n {data.requestId:check}ID {data.requestId:end-check-exists} {data.requestSsn:start-check-exists}\n {data.requestSsn:check}Social Security # {data.requestSsn:end-check-exists} {data.requestLeaseOrOwnership:start-check-exists}\n {data.requestLeaseOrOwnership:check}Lease or Ownership {data.requestLeaseOrOwnership:end-check-exists} {data.requestSecondFormId:start-check-exists}\n {data.requestSecondFormId:check}Second Form of ID ( Passport, Birth Certificate, Visa, Permanent Resident Card, Counselor Card, Native American Tribal ID, Military ID ) {data.requestSecondFormId:end-check-exists} {data.paperworkReceived:start-check-exists}\n{data.paperworkReceived:check} Paperwork received, you will be contacted once service request review is completed.  {data.paperworkReceived:end-check-exists} {data.requestNotarizedInformation:start-check-exists}\n{data.requestNotarizedInformation:check} Request for notarized information - Received documentation, unable to approve, please resend ID notarized to complete service request.  {data.requestNotarizedInformation:end-check-exists} {data.idValidatedAndAuthorized:start-check-exists}\n{data.idValidatedAndAuthorized:check} Received documentation, validated ID, approved for service request. {data.idValidatedAndAuthorized:end-check-exists} {data.idValidatedAndNotAuthorized:start-check-exists}\n{data.idValidatedAndNotAuthorized:check} Received documentation, validated ID, unable to approve service request. {data.idValidatedAndNotAuthorized:end-check-exists} {data.idNotValidatedAndNotAuthorized:start-check-exists}\n{data.idNotValidatedAndNotAuthorized:check} Received documentation, unable to validate ID, not able to approve service request. {data.idNotValidatedAndNotAuthorized:end-check-exists}\n\nFor additional information, please use the contact information below.\n\nFax 1-866-703-8115\n\nHotline 1-810-760-3245\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-230642", "updatedAt": {"$date": "2022-12-12T20:22:14.873Z"}, "createdAt": {"$date": "2022-08-04T19:14:17.435Z"}, "__v": 0}, {"_id": {"$oid": "62e2cb5049813f0095f09e51"}, "templateName": "sms-frt-pir", "templateHtml": "PIR (Follow-up on request for services)\nAccount Number: ending in {accountNumber:last-chars:4}\nService Address: {serviceAddress}\n\nThank you for contacting Consumers Energy.\n{data.requestDocumentation:start-check-exists}\n{data.requestDocumentation:check} Request for documentation {data.requestDocumentation:end-check-exists}  {data.requestPirId:start-check-exists}\n   {data.requestPirId:check} ID {data.requestPirId:end-check-exists}  {data.requestPirSsn:start-check-exists}\n   {data.requestPirSsn:check} Social Security # {data.requestPirSsn:end-check-exists}  {data.requestPirLeaseOrOwnership:start-check-exists}\n   {data.requestPirLeaseOrOwnership:check} Lease or Ownership {data.requestPirLeaseOrOwnership:end-check-exists}\n{data.pirPaperworkReceived:start-check-exists}\n{data.pirPaperworkReceived:check} Paperwork received {data.pirPaperworkReceived:end-check-exists}  {data.businessDaysForReview13:start-check-exists}\n   {data.businessDaysForReview13:check} 1-3 business days for review {data.businessDaysForReview13:end-check-exists}  {data.businessDaysForReview35:start-check-exists}\n   {data.businessDaysForReview35:check} 3-5 business days for review {data.businessDaysForReview35:end-check-exists}\n{data.requestAuthorized:start-check-exists}\n{data.requestAuthorized:check} Received documentation, approved for service request {data.requestAuthorized:end-check-exists} {data.requestNotAuthorized:start-check-exists}\n{data.requestNotAuthorized:check} Received documentation, unable to approve your service request{data.requestNotAuthorized:end-check-exists}\n{data.approvedServiceRequest:start-check-exists}\n{data.approvedServiceRequest:check} Approved for service request{data.approvedServiceRequest:end-check-exists}\n\nFor additional information, please use the contact information below.\n\nFax 1-866-703-8115\n\nHotline 1-810-760-3245\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-200532", "updatedAt": {"$date": "2022-12-12T20:22:14.876Z"}, "createdAt": {"$date": "2022-08-04T19:14:17.435Z"}, "__v": 0}, {"_id": {"$oid": "62e2c5873b0f3198b31216c4"}, "templateName": "email-frt-pir", "templateHtml": "<img src=\"app-api/images/stored/top_frt-pir.png\"><br><br>\nAccount Number:&nbsp; ending in&nbsp;{accountNumber:last-chars:4}<br>\nService Address:&nbsp; &nbsp; {serviceAddress}<table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\">\n<br><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br><br>Thank you for contacting Consumers Energy.\n<br>{data.requestDocumentation:start-check-exists}<br><b>{data.requestDocumentation:check} Request for documentation&nbsp;</b>{data.requestDocumentation:end-check-exists}\n {data.requestPirId:start-check-exists}<br><b>&nbsp; &nbsp; {data.requestPirId:check} ID&nbsp;</b>{data.requestPirId:end-check-exists} \n {data.requestPirSsn:start-check-exists}<br><b>&nbsp; &nbsp; {data.requestPirSsn:check}&nbsp;Social Security #&nbsp;</b>{data.requestPirSsn:end-check-exists} \n {data.requestPirLeaseOrOwnership:start-check-exists}<br><b>&nbsp; &nbsp; {data.requestPirLeaseOrOwnership:check} Lease or Ownership&nbsp;</b>{data.requestPirLeaseOrOwnership:end-check-exists}\n<br>\n{data.pirPaperworkReceived:start-check-exists}<br><b>{data.pirPaperworkReceived:check} Paperwork received&nbsp;</b>{data.pirPaperworkReceived:end-check-exists}\n {data.businessDaysForReview13:start-check-exists}<br><b>&nbsp; &nbsp; {data.businessDaysForReview13:check} 1-3 business days for review&nbsp;</b>{data.businessDaysForReview13:end-check-exists}\n {data.businessDaysForReview35:start-check-exists}<br><b>&nbsp; &nbsp; {data.businessDaysForReview35:check} 3-5 business days for review&nbsp;</b>{data.businessDaysForReview35:end-check-exists}\n<br>\n{data.requestAuthorized:start-check-exists}<br><b>{data.requestAuthorized:check}&nbsp;Received documentation, approved for service request&nbsp;</b>{data.requestAuthorized:end-check-exists}\n{data.requestNotAuthorized:start-check-exists}\n<br><b>{data.requestNotAuthorized:check}&nbsp;Received documentation, unable to approve your service request</b>{data.requestNotAuthorized:end-check-exists} {data.approvedServiceRequest:start-check-exists}<br><b>{data.approvedServiceRequest:check}&nbsp;Approved for service request</b>{data.approvedServiceRequest:end-check-exists}<br><br>For additional information, please use the contact information below.<br><br>Fax 1-866-703-8115<br><br>Hotline 1-810-760-3245<br> <br> Sincerely,<br><br> Consumers Energy<br><br>Please do not reply to this automated email message.</td><td><br></td></tr></tbody></table> &nbsp;", "isActive": true, "version": "********-200647", "updatedAt": {"$date": "2022-12-12T20:22:14.880Z"}, "createdAt": {"$date": "2022-08-04T19:14:17.435Z"}, "__v": 0}, {"_id": {"$oid": "62e2bdf50ef9aa7f77892daf"}, "templateName": "email-frt-facta-id", "templateHtml": "<img src=\"app-api/images/stored/top_frt-fatca.png\"><br><br>\nAccount Number:&nbsp; ending in&nbsp;{accountNumber:last-chars:4}<br>\nService Address:&nbsp; &nbsp; {serviceAddress}<table width=\"100%\" cellspacing=\"0\" border=\"0\">\n<tbody><tr><td width=\"950\"><br>Thank you for contacting Consumers Energy<br>{data.requestDocumentation:start-check-exists}<br><b>{data.requestDocumentation:check} Request for documentation&nbsp;</b>{data.requestDocumentation:end-check-exists}{data.requestId:start-check-exists}<br><b> {data.requestId:check}ID </b>{data.requestId:end-check-exists} {data.requestSsn:start-check-exists}<br><b> {data.requestSsn:check}Social Security #&nbsp;</b>{data.requestSsn:end-check-exists} {data.requestLeaseOrOwnership:start-check-exists}<br><b> {data.requestLeaseOrOwnership:check}Lease or Ownership&nbsp;</b>{data.requestLeaseOrOwnership:end-check-exists} {data.requestSecondFormId:start-check-exists}<br><b> {data.requestSecondFormId:check}Second Form of ID ( Passport, Birth Certificate, Visa, Permanent Resident Card, Counselor Card, Native American Tribal ID, Military ID )&nbsp;</b>{data.requestSecondFormId:end-check-exists} {data.paperworkReceived:start-check-exists}<br><b>{data.paperworkReceived:check}&nbsp;Paperwork received, you will be contacted once service request review is completed.&nbsp;&nbsp;</b>{data.paperworkReceived:end-check-exists}\n{data.requestNotarizedInformation:start-check-exists}<br><b>{data.requestNotarizedInformation:check}&nbsp;Request for notarized information - Received documentation, unable to approve, please resend ID notarized to complete service request.&nbsp;&nbsp;</b>{data.requestNotarizedInformation:end-check-exists}\n{data.idValidatedAndAuthorized:start-check-exists}<br><b>{data.idValidatedAndAuthorized:check}&nbsp;Received documentation, validated ID, approved for service request.&nbsp;</b>{data.idValidatedAndAuthorized:end-check-exists}\n{data.idValidatedAndNotAuthorized:start-check-exists}<br><b>{data.idValidatedAndNotAuthorized:check}&nbsp;Received documentation, validated ID, unable to approve service request.&nbsp;</b>{data.idValidatedAndNotAuthorized:end-check-exists}\n{data.idNotValidatedAndNotAuthorized:start-check-exists}<br><b>{data.idNotValidatedAndNotAuthorized:check}&nbsp;Received documentation, unable to validate ID, not able to approve service request.&nbsp;</b>{data.idNotValidatedAndNotAuthorized:end-check-exists}\n<br><br>For additional information, please use the contact information below.<br><br>Fax 1-866-703-8115<br><br>Hotline 1-810-760-3245<br> <br> Sincerely,<br><br> Consumers Energy<br><br>Please do not reply to this automated email message.</td></tr></tbody></table> &nbsp;", "isActive": true, "version": "********-230815", "updatedAt": {"$date": "2022-12-12T20:22:14.883Z"}, "createdAt": {"$date": "2022-08-04T19:14:17.435Z"}, "__v": 0}, {"_id": {"$oid": "62e06bbe7098f96ad1541c9d"}, "templateName": "sms-csr-standard-doc-forms-dgen", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle}. Please click the link to continue your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/residential/renewable-energy/distributed-generation-program-guide.ashx \n\nPlease do not reply to this automated message.", "isActive": true, "version": "20220726-223334", "updatedAt": {"$date": "2022-12-12T20:22:14.886Z"}, "createdAt": {"$date": "2022-08-11T23:06:16.685Z"}, "__v": 0}, {"_id": {"$oid": "62e06b497098f96ad1541c96"}, "templateName": "email-csr-standard-doc-forms-dgen", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_dgen.png\" style=\"width:960px;\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p>Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle}. Please click the link to continue your request and view documentation required.</p><div><br></div><p><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/residential/renewable-energy/distributed-generation-program-guide.ashx\" target=\"_blank\">https://www.consumersenergy.com/-/media/CE/Documents/residential/renewable-energy/distributed-generation-program-guide.ashx</a><span style=\"background-color: transparent;\"><br></span><br></p><p>Sincerely,</p><p>Consumers Energy</p><p><br></p><p>Please do not reply to this automated message.</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div>", "isActive": true, "version": "20220803-231520", "updatedAt": {"$date": "2022-12-12T20:22:14.890Z"}, "createdAt": {"$date": "2022-08-11T23:06:16.685Z"}, "__v": 0}, {"_id": {"$oid": "62df0b8eedb947803f0a97f7"}, "templateName": "sms-csr-standard-doc-forms-redx", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle}. Please click the link to continue your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/2333", "isActive": true, "version": "********-213054", "updatedAt": {"$date": "2022-12-12T20:22:14.893Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62df0b67edb947803f0a97f2"}, "templateName": "email-csr-standard-doc-forms-redx", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_redx.png\" style=\"width:960px;\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p>Thank you for calling Consumers Energy. Attached is your request for {<font face=\"Arial\">data.</font>serviceOfferingTitle}. Please click the link to continue your request and view documentation required.<br></p><p><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer Forms/2333\" target=\"_blank\">https://www.consumersenergy.com/-/media/CE/Documents/Customer Forms/2333</a><span style=\"background-color: transparent;\"><br></span><br></p><p>Sincerely,</p><p>Consumers Energy</p><p><br></p><p>Please do not reply to this automated message.</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div>", "isActive": true, "version": "20220803-231858", "updatedAt": {"$date": "2022-12-12T20:22:14.896Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62df0b1fedb947803f0a97e9"}, "templateName": "sms-csr-standard-doc-forms-ste", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle}. Please click the link to continue your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1830", "isActive": true, "version": "********-212903", "updatedAt": {"$date": "2022-12-12T20:22:14.899Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62df0afbedb947803f0a97e4"}, "templateName": "email-csr-standard-doc-forms-ste", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_ste.png\" style=\"width:960px;\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p>Thank you for calling Consumers Energy. Attached is your request for {<font face=\"Arial\">data.</font>serviceOfferingTitle}. Please click the link to continue your request and view documentation required.<br></p><p><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer Forms/1830\" target=\"_blank\">https://www.consumersenergy.com/-/media/CE/Documents/Customer Forms/1830</a><span style=\"background-color: transparent;\"><br></span><br></p><p>Sincerely,</p><p>Consumers Energy</p><p><br></p><p>Please do not reply to this automated message.</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div>", "isActive": true, "version": "20220803-231158", "updatedAt": {"$date": "2022-12-12T20:22:14.901Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62df0abdedb947803f0a97dc"}, "templateName": "sms-csr-standard-doc-forms-ria", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle}. Please click the link to continue your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1933", "isActive": true, "version": "********-212725", "updatedAt": {"$date": "2022-12-12T20:22:14.904Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62df0a93edb947803f0a97d6"}, "templateName": "email-csr-standard-doc-forms-ria", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_ria.png\" style=\"width:960px;\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p>Thank you for calling Consumers Energy. Attached is your request for {<font face=\"Arial\">data.</font>serviceOfferingTitle}. Please click the link to continue your request and view documentation required.<br></p><p><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer Forms/1933\" target=\"_blank\">https://www.consumersenergy.com/-/media/CE/Documents/Customer Forms/1933</a><span style=\"background-color: transparent;\"><br></span><br></p><p>Sincerely,</p><p>Consumers Energy</p><p><br></p><p>Please do not reply to this automated message.</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div>", "isActive": true, "version": "20220803-231243", "updatedAt": {"$date": "2022-12-12T20:22:14.907Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62df0a4bedb947803f0a97ce"}, "templateName": "sms-csr-standard-doc-forms-loam", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle}. Please click the link below to continue with your request and view documentation required.\n\nhttps://www.consumersenergy.com/privacy/letter-of-authorization-multiple-addresses", "isActive": true, "version": "********-212531", "updatedAt": {"$date": "2022-12-12T20:22:14.910Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62df0a09edb947803f0a97c8"}, "templateName": "email-csr-standard-doc-forms-loam", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_loam.png\" style=\"width:960px;\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>Thank you for calling Consumers Energy to request a&nbsp; {data.serviceOfferingTitle} which includes&nbsp; {serviceAddress}.</p><p>Please click the link below to continue with your request and view documentation required.<br></p><p><span style=\"background-color: transparent;\">Visit:</span><br></p><p><a href=\"https://www.consumersenergy.com/privacy/letter-of-authorization-multiple-addresses\" target=\"_blank\">Non-Residential Letter of Authorization (LOA) Multiple Requests | Consumers Energy</a><span style=\"background-color: transparent;\"><br></span><br></p><p><span style=\"background-color: transparent;\">Note: The Letter of Authorization - Multiple Requests is used for business customers set up as an organization. This form gives authorization for one year.</span><br></p><p><span style=\"background-color: transparent;\">This form requires the following steps:</span><br></p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><font size=\"5\" color=\"#004993\">❶</font>&nbsp;<span style=\"background-color: transparent;\">Fill out all required fields. All fields are required unless otherwise indicated. (Note: You are able to give authorization on multiple addresses or account numbers)<br></span><font size=\"5\" color=\"#004993\">❷</font>&nbsp;<span style=\"background-color: transparent;\">Once form is completed, please either email or mail the form to:</span></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><span style=\"background-color: transparent;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\">LANDLORD &amp; SMALL BUSINESS TEAM - ROOM 214<br>C/O CONSUMERS ENERGY<br>4000 CLAY AVENUE SW<br>GRAND RAPIDS, MI&nbsp; 49548<br><a href=\"mailto:<EMAIL>\"><EMAIL></a></blockquote></span></blockquote></blockquote>Once all required documentation has been received, your request will be completed within 10 business days. If you have any questions, please contact us at ************.<br><br><br>Sincerely,<br>Consumers Energy<br><p>Please do not reply to this automated email message</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "20220803-231035", "updatedAt": {"$date": "2022-12-12T20:22:14.912Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62df07adedb947803f0a97b7"}, "templateName": "sms-csr-standard-doc-forms-sav", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle}. Please click the link to continue your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Energy%20Efficiency/more-100-ways-save-on-bill.ashx\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-211421", "updatedAt": {"$date": "2022-12-12T20:22:14.916Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62def550edb947803f0a974c"}, "templateName": "email-csr-standard-doc-forms-sav", "templateHtml": "<img src=\"app-api/images/stored/100-ways.png\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; {accountNumber}<br>Service Address:&nbsp; &nbsp;&nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><br><br>Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle}. Please click the link to continue your request and view documentation required.<table width=\"100%\" cellspacing=\"0\" border=\"0\"></table><br><table width=\"100%\" cellspacing=\"0\" border=\"0\"></table><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Energy Efficiency/more-100-ways-save-on-bill.ashx\" target=\"_blank\">https://www.consumersenergy.com/-/media/CE/Documents/Energy Efficiency/more-100-ways-save-on-bill.ashx</a><br><table width=\"100%\" cellspacing=\"0\" border=\"0\"></table>Sincerely,<table width=\"100%\" cellspacing=\"0\" border=\"0\"></table><br><table width=\"100%\" cellspacing=\"0\" border=\"0\"></table>Consumers Energy<table width=\"100%\" cellspacing=\"0\" border=\"0\"></table><br><table width=\"100%\" cellspacing=\"0\" border=\"0\"></table>&nbsp;<table width=\"100%\" cellspacing=\"0\" border=\"0\"></table><br><table width=\"100%\" cellspacing=\"0\" border=\"0\"></table>Please do not reply to this automated message.<table width=\"100%\" cellspacing=\"0\" border=\"0\"></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody></tbody></table> &nbsp;", "isActive": true, "version": "********-195600", "updatedAt": {"$date": "2022-12-12T20:22:14.919Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62def4f4edb947803f0a9744"}, "templateName": "sms-csr-standard-doc-forms-daf", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for the {data.serviceOfferingTitle}. Please click the link to continue your request and view documentation required. Please ensure that your form has been notarized unless proof of authority to act on decedent behalf has already been provided.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/affidavit-estate \n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-195428", "updatedAt": {"$date": "2022-12-12T20:22:14.922Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62def47cedb947803f0a973d"}, "templateName": "email-csr-standard-doc-forms-daf", "templateHtml": "<img src=\"app-api/images/stored/deceased.png\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; {accountNumber}<br>Service Address:&nbsp; &nbsp;&nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><br><br><div>Thank you for calling Consumers Energy. Attached is your request for the {data.serviceOfferingTitle}. Please click the link to continue your request and view documentation required. Please ensure that your form has been notarized unless proof of authority to act on decedent behalf has already been provided.</div><div><br></div><div><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer Forms/affidavit-estate\" target=\"_blank\">https://www.consumersenergy.com/-/media/CE/Documents/Customer Forms/affidavit-estate</a><br></div><div><br></div><div>Sincerely,</div><div><br></div><div>Consumers Energy</div><div><br></div><div>&nbsp;</div><div><br></div><div>Please do not reply to this automated message.</div>", "isActive": true, "version": "********-195228", "updatedAt": {"$date": "2022-12-12T20:22:14.924Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62dee54fd396ad91f88026ca"}, "templateName": "sms-csr-damage-claims", "templateHtml": "Consumers Energy Damage Claims Notification # {data.dcNotifyNumber}\n\nThank you for reaching out to Consumers Energy. If you need immediate assistance, please work with your insurance provider. Your claim has been submitted and is currently being investigated to determine liability. You will receive communication from Consumers Energy within 3-5 business days.\n\n\nThank you for being a valued Consumers Energy customer.\n", "isActive": true, "version": "********-184743", "updatedAt": {"$date": "2022-12-12T20:22:14.927Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62dee53bd396ad91f88026c5"}, "templateName": "email-csr-damage-claims", "templateHtml": "<img src=\"app-api/images/stored/damage-claim.png\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;&nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br>Damage Claim - Order Confirmation for Damage Claim<br><br>Thank you for reaching out to Consumers Energy. If you need immediate assistance, please work with your insurance provider. Your claim has been submitted and is currently being investigated to determine liability. You will receive communication from Consumers Energy within 3-5 business days.<br><br>Thank you for being a valued Consumers Energy customer,<br><br><img src=\"app-api/images/stored/CE_signatureLogo.jpg\"><br>This response is from an unmonitored email. Please do not respond.<br><br></td><td><br></td></tr></tbody></table> &nbsp;", "isActive": true, "version": "********-185002", "updatedAt": {"$date": "2022-12-12T20:22:14.929Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62dee27fd396ad91f8802621"}, "templateName": "sms-csr-standard-doc-forms-mcf", "templateHtml": "Here is the link to the {data.serviceOfferingTitle} Form you requested:\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/2330", "isActive": true, "version": "********-183543", "updatedAt": {"$date": "2022-12-12T20:22:14.932Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62dee26ed396ad91f880261c"}, "templateName": "email-csr-standard-doc-forms-mcf", "templateHtml": "<img src=\"app-api/images/stored/top_mcf.jpg\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;&nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br></div><div><div>Here is the link to the {data.serviceOfferingTitle} Form you requested:</div><div><br></div><div>https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/2330</div></div>", "isActive": true, "version": "********-184850", "updatedAt": {"$date": "2022-12-12T20:22:14.934Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62dee216d396ad91f880260d"}, "templateName": "sms-csr-standard-doc-forms-hhc", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle} Information Form. Please click the links below to continue with your request and view documentation required.\n\nhttps://www.michigan.gov/documents/taxes/MI-1040CR7_745688_7.pdf\n\nhttps://www.michigan.gov/documents/taxes/BOOK_MI-1040CR-7_745779_7.pdf", "isActive": true, "version": "********-183358", "updatedAt": {"$date": "2022-12-12T20:22:14.937Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62dee203d396ad91f8802608"}, "templateName": "email-csr-standard-doc-forms-hhc", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_hhc.jpg\" width=\"960\" height=\"238\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>Thank you for calling Consumers Energy to request a {data.serviceOfferingTitle} at the service address of <font color=\"#004993\">{serviceAddress}</font>.</p><p>Please click the links below to continue with your request and view documentation required. Visit&nbsp;<br><br><span style=\"background-color: transparent;\"><a href=\"https://www.michigan.gov/documents/taxes/MI-1040CR7_745688_7.pdf\" target=\"_blank\" rel=\"noopener noreferrer\">2021 MICHIGAN Home Heating Credit Claim MI-1040CR-7 [michigan.gov]</a><br><br><a href=\"https://www.michigan.gov/documents/taxes/BOOK_MI-1040CR-7_745779_7.pdf\" target=\"_blank\" rel=\"noopener noreferrer\">2021 Book MI-1040CR7 Home Heating Instructions (michigan.gov) [michigan.gov]</a><br></span></p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><ul><li><span style=\"background-color: transparent;\">The MI-1040CR-7 form can be completed electronically or through the mail</span></li><ul><li><span style=\"background-color: transparent;\">Self-serve instructions for the form can be found online under “Forms and Instructions”</span></li></ul><li><span style=\"background-color: transparent;\">To obtain forms, get help filling out a form, or check on your credit with the State of Michigan - 517-636-4486</span></li><li><span style=\"background-color: transparent;\">Your heating costs can be found on your January – June bills month, your eservices account, or by contacting Consumers Energy at **************</span></li></ul></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b style=\"background-color: transparent;\"><br></b></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b style=\"background-color: transparent;\"><br></b></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"></blockquote><b style=\"background-color: transparent;\">The State of Michigan's deadline for filing is September 30 each year.</b><br><br><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"></blockquote>Sincerely,<br><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><p><br></p></blockquote>Consumers Energy<br><p>Please do not respond to this automated message</p></td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "********-183339", "updatedAt": {"$date": "2022-12-12T20:22:14.940Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62dee156d396ad91f88025f3"}, "templateName": "sms-csr-standard-doc-forms-tpa", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle} Form. Please click the link below to continue with your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1394.ashx", "isActive": true, "version": "********-183046", "updatedAt": {"$date": "2022-12-12T20:22:14.943Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62dee141d396ad91f88025ee"}, "templateName": "email-csr-standard-doc-forms-tpa", "templateHtml": "<div style=\"width: 960px;\">\n\t<br><table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_tpn.jpg\" style=\"width:960px;\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>Thank you for calling Consumers Energy to request a {data.serviceOfferingTitle} form for service address of {serviceAddress}.</p><p>Please click the link below to continue with your request and view documentation required.&nbsp;</p><p>Visit:</p><p><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1394.ashx\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1394.ashx</a><br>&nbsp;</p><p>This form requires the following steps:</p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><font size=\"5\" color=\"#004993\">❶</font>&nbsp;<span style=\"background-color: transparent;\">Fill out all required fields. All fields are required unless otherwise indicated. (Note: The&nbsp;</span>{data.serviceOfferingTitle}<span style=\"background-color: transparent;\">&nbsp;form needs to be filled out by both the Consumers Energy customer and the Third Party intended to receive notification)<br></span><font size=\"5\" color=\"#004993\">❷</font>&nbsp;<span style=\"background-color: transparent;\">Once form is completed, please either email, fax, or mail the form to:</span></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><span style=\"background-color: transparent;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b>Email:&nbsp;</b><a href=\"mailto:<EMAIL>\" style=\"background-color: transparent;\"><EMAIL></a></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b>Fax:&nbsp;</b><span style=\"background-color: transparent;\">517-325-8221</span></blockquote></span><span style=\"background-color: transparent;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b>Mail:</b></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\">Lansing Consumer Affairs, Room 214<br>Consumers Energy<br>PO Box 30162<br>Lansing, MI 48909-7662<br></blockquote></span></blockquote>Once all required documentation has been received, your request will be completed within 10 business days. If you have any questions, please contact us at ************.<br><br><br>Sincerely,<br>Consumers Energy<br><p>Please do not reply to this automated email message</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "20220803-231754", "updatedAt": {"$date": "2022-12-12T20:22:14.946Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62dee0abd396ad91f88025c2"}, "templateName": "email-csr-standard-doc-forms-roci", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_roci.jpg\" style=\"width:960px;\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>Thank you for calling Consumers Energy to request a {data.serviceOfferingTitle} form for service address of {serviceAddress}.</p><p>Please click the link below to continue with your request and view documentation required.&nbsp;</p><p>Visit:</p><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1619.ashx\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1619.ashx</a><br><p>Note: The {data.serviceOfferingTitle} form is used for <u>residential accounts</u>.&nbsp;</p><p>This form requires the following steps:</p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><font size=\"5\" color=\"#004993\">❶</font>&nbsp;<span style=\"background-color: transparent;\">Fill out all required fields. All fields are required unless otherwise indicated. (Note: You are able to give authorization on multiple addresses or account numbers)<br></span><font size=\"5\" color=\"#004993\">❷</font>&nbsp;<span style=\"background-color: transparent;\">Once form is completed, please either email or mail the form to:</span></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><span style=\"background-color: transparent;\">Consumers Energy Customer Service&nbsp;</span><br>4000 Clay Avenue SW&nbsp;<br>Grand Rapids, MI 49548-301&nbsp;<br><a href=\"mailto:<EMAIL>\"><EMAIL></a></blockquote></blockquote>You may also take the form into any Consumers Energy Direct Payment Office.<br><br>Once all required documentation has been received, your request will be completed within 10 business days. If you have any questions, please contact us at ************.<br><br>Sincerely,<br>Consumers Energy<br><p>Please do not reply to this automated email message</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "20220803-231608", "updatedAt": {"$date": "2022-12-12T20:22:14.948Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62dee079d396ad91f88025bb"}, "templateName": "sms-csr-standard-doc-forms-roci", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle} Form. Please click the link below to continue with your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1619.ashx", "isActive": true, "version": "********-182705", "updatedAt": {"$date": "2022-12-12T20:22:14.951Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62de407bd396ad91f8802389"}, "templateName": "email-csr-standard-doc-forms-loa", "templateHtml": "<div style=\"width: 960px;\">\n<table style=\"width: 960px;\">\n<tbody>\n  <tr>\n    <td>\n      <img src=\"app-api/images/stored/top_loa.jpg\" style=\"width:960px;\"></td>\n    </tr>\n    <tr>\n      <td>\n        <p>\n        </p><div style=\"text-align: justify;margin-top: -70px;\">\n          <table style=\"width: 960px; text-align: start;\">\n            <tbody>\n              <tr>\n                <td style=\"width: 150px;\"><br></td>\n                <td><br></td></tr>\n            </tbody>\n          </table>\n          </div>\n          <p><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br></p>\n            <p>\n            </p><p>Thank you for calling Consumers Energy to request a {data.serviceOfferingTitle} for service address of {serviceAddress}<span style=\"background-color: transparent;\">.</span></p><p><span style=\"background-color: transparent;\">Please click the link below to continue with your request and view documentation required.&nbsp;</span><br></p><p><span style=\"background-color: transparent;\">Visit:</span><br></p><p><span style=\"background-color: transparent;\"><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/business/letter-of-authorization-business-customers.ashx\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.consumersenergy.com/-/media/CE/Documents/business/letter-of-authorization-business-customers.ashx</a><br></span><br></p><p><span style=\"background-color: transparent;\">Note: The Letter of Authorization is used for business customers set up as an organization. This form gives authorization for one year.</span><br></p><p><span style=\"background-color: transparent;\">This form requires the following steps:</span><br></p><blockquote style=\"margin: 0px 0px 0px 40px; border: none; padding: 0px;\"><font size=\"5\" color=\"#004993\">❶</font>&nbsp;<span style=\"background-color: transparent;\">Fill out all required fields. All fields are required unless otherwise indicated. (Note: You are able to give authorization on multiple addresses or account numbers)<br></span><font size=\"5\" color=\"#004993\">❷</font>&nbsp;<span style=\"background-color: transparent;\">Once form is completed, please either email or mail the form to:</span></blockquote><blockquote style=\"margin: 0px 0px 0px 40px; border: none; padding: 0px;\"><blockquote style=\"margin: 0px 0px 0px 40px; border: none; padding: 0px;\"><span style=\"background-color: transparent;\"><blockquote style=\"margin: 0px 0px 0px 40px; border: none; padding: 0px;\">LANDLORD &amp; SMALL BUSINESS TEAM - ROOM 214<br>C/O CONSUMERS ENERGY<br>4000 CLAY AVENUE SW<br>GRAND RAPIDS, MI&nbsp; 49548<br><a href=\"mailto:<EMAIL>\"><EMAIL></a></blockquote></span></blockquote></blockquote><p>Once all required documentation has been received, your request will be completed within 10 business days. If you have any questions, please contact us at ************.<br><br><br>Sincerely,<br>Consumers Energy<br></p><p>Please do not reply to this automated email message</p>\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n            </div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "20220810-081935", "updatedAt": {"$date": "2022-12-12T20:22:14.953Z"}, "createdAt": {"$date": "2022-08-11T16:50:09.497Z"}, "__v": 0}, {"_id": {"$oid": "62de3ee5d396ad91f8802203"}, "templateName": "sms-csr-standard-doc-forms-loa", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {data.serviceOfferingTitle} Information Form. Please click the link below to continue with your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/business/letter-of-authorization-business-customers.ashx", "isActive": true, "version": "********-070157", "updatedAt": {"$date": "2022-12-12T20:22:14.957Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62da8ae2788b926316ac3e73"}, "templateName": "email-csr-installment-plan", "templateHtml": "<img src=\"app-api/images/stored/top-pac.png\">\n<table>\n<tbody>\n<tr>\n  <td>Account Number:</td>\n  <td>ending in {accountNumber:last-chars:4}</td>\n</tr>\n<tr>\n  <td>Service Address:</td>\n  <td>{serviceAddress}</td>\n</tr>\n</tbody>\n</table>\n<br><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font>\n    </b>\n    <br>\n    <br>Thank you for calling Consumers Energy on {currentDate} to make a\n    payment arrangement on your account.&nbsp; We've completed your request\n    for a payment arrangement of {data.numberPayments} {data.paymentFrequency:lowerCase}\n    payments.<br>\n    <br>Your first payment of ${data.payments.0.amount:de} is due on\n    {data.payments.0.date:dt}.<br>\n    <br>Payment Arrangement Schedule:<br>\n    <div style=\"min-width: auto;margin-top: 20px;margin-left: 50px;\">\n\n          <div>\n      <span style=\"display: inline-block;background-color: #004993;color: white;font-size: 12px;width: 120px;font-weight: bold;\">Payment Amount</span>\n      <span style=\"display: inline-block;background-color: #004993;color: white;font-size: 12px;width: 70px;font-weight: bold;\">Due Date</span>\n    </div>\n\n      {data.payments:start-loop}\n\n\n     <div>\n      <span style=\"display: inline-block;background-color: #ccffcc;font-size: 12px;width: 120px;\">${amount:de}</span>\n      <span style=\"display: inline-block;background-color: #ccffcc;font-size: 12px;width: 70px;\">{date:dt}</span>\n</div>\n      {data.payments:end-loop}\n    </div>\n    <br>\n    <font size=\"2\">\n      <b>Please Note:</b>&nbsp;<i>This arrangement does not include any future charges.&nbsp; All\n        additional charges must be paid in addition to your arrangement.</i>\n    </font>\n    <br>\n    <br>Please ensure you allow sufficient time for your payment to be\n    received on or before {data.payments.0.date:dt}.&nbsp; If you are concerned about your\n    payment posting on time, we offer several options to pay your bill that\n    post to your account immediately! Click&nbsp;<a href=\"https://www.consumersenergy.com/residential/billing-and-payment/payment\" target=\"_blank\">here</a>&nbsp;to learn more.<br>\n    <br>Sincerely,<br>\n    <br>Consumers Energy<br>\n    <br>Please do not reply to this automated email message.<br>\n  </td>\n\n  <td>\n    <br>\n  </td>\n</tr>\n</tbody>\n</table>\n\n", "isActive": true, "version": "********-155841", "updatedAt": {"$date": "2022-12-12T20:22:14.959Z"}, "createdAt": {"$date": "2022-08-11T16:50:09.497Z"}, "__v": 0}, {"_id": {"$oid": "62da8997788b926316ac3e3b"}, "templateName": "email-csr-shutoff-hold-mep", "templateHtml": "<img src=\"app-api/images/stored/top-tsohc.png\"><br><table><tbody><tr><td>Account Number:</td><td>ending in {accountNumber:last-chars:4}</td></tr><tr><td>Service Address:</td><td>{serviceAddress}</td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br><br>Thank you for calling Consumers Energy on {currentDate:dt} to make a payment arrangement on your past due account.. We’ve completed your request fora {data.holdDays}-day temporary shut-off hold effective immediately under a Critical Care Hold. During this time your account will be protected from shut-offs.<br><br>Your shut-off hold will expire on&nbsp;<font size=\"4\" color=\"#004993\" style=\"\"><b>{data.holdExpDate:dt}</b></font>.<br><br>You will need to make a payment on your account before this date to avoid the risk of shut-off after the hold expires.&nbsp; If you need help paying your bill, you can call 2-1-1 to learn about other program that might be able to help with your utility costs.<br><br>If you have a qualifying, documented medical emergency,&nbsp;&nbsp;<font size=\"4\" color=\"#004993\"><b>Medical Emergency Protection</b></font>&nbsp;protects you from an energy service shut-off for up to <font size=\"4\" color=\"#004993\"><b> 21</b></font> days. You can submit an additional certificate every three weeks for a total of 63 days if the medical emergency continues that long.<br><br>This programs requires you to send Consumers Energy proof from a doctor or a notice from a public health official that service shut-off will make an existing medical problem worse.&nbsp; Visit&nbsp;<a href=\"http://www.ConsumersEnergy.com/lifesupport\" target=\"_blank\">www.ConsumersEnergy.com/lifesupport</a>&nbsp;for a copy of the Medical Certification Form. The form includes directions on how to complete and submit it.<br><br><i><font size=\"2\">PLEASE NOTE: Backup generators and transportation services are not part of this program. Participation in this program does not mean your electric power will be restored sooner than others if there is a power outage from a storm or other event.</font></i><br><br>Sincerely,<br><br>Consumers Energy<br><br><br>Please do not reply to this automated email message.</td><td><br></td></tr></tbody></table>", "isActive": true, "version": "********-112719", "updatedAt": {"$date": "2022-12-12T20:22:14.962Z"}, "createdAt": {"$date": "2022-08-11T16:50:09.497Z"}, "__v": 0}, {"_id": {"$oid": "62da894c788b926316ac3e32"}, "templateName": "sms-csr-shutoff-hold-mep", "templateHtml": "Consumers Energy: PAYMENT ARRANGEMENT CONFIRMATION\n\nAccount: {serviceAddress}\nHold Expiration Date: {data.holdExpDate:dt}\nReason for Hold: Medical Emergency Protection\n\nThis programs requires you to send Consumers Energy proof from a doctor or a notice from a public health official that service shut-off will make an existing medical problem worse.\n\nVisit http://www.ConsumersEnergy.com/lifesupport for a copy of the Medical Certification Form. The form includes directions on how to complete and submit it.", "isActive": true, "version": "********-112817", "updatedAt": {"$date": "2022-12-12T20:22:14.964Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62da884a788b926316ac3df2"}, "templateName": "email-csr-shutoff-hold-spp", "templateHtml": "<img src=\"app-api/images/stored/spp.png\"><br><table><tbody><tr><td>Account\n \nNumber:</td><td>ending in {accountNumber:last-chars:4}</td></tr><tr><td>Service\n \nAddress:</td><td>{serviceAddress}</td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\">\n    <tbody><tr>\n        <td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {firstName} \n{lastName},</font></b><br><br>Thank you for \ncalling Consumers Energy on {currentDate:dt} to make a payment arrangement\n on your past due balance. We’ve submitted your request to enroll in \nthe&nbsp;shut-off protection plan. To complete your plan enrollment,\n a down payment must be posted to your account within <span style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\nmso-fareast-font-family:<PERSON>ibri;mso-fareast-theme-font:minor-latin;mso-ansi-language:\nEN-US;mso-fareast-language:EN-US;mso-bidi-language:AR-SA\">{data.holdDays}-</span><!--[if\n gte mso 9]><xml>\n <w:WordDocument>\n  <w:View>Normal</w:View>\n  <w:Zoom>0</w:Zoom>\n  <w:TrackMoves/>\n  <w:TrackFormatting/>\n  <w:PunctuationKerning/>\n  <w:ValidateAgainstSchemas/>\n  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>\n  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>\n  \n<w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>\n  <w:DoNotPromoteQF/>\n  <w:LidThemeOther>EN-US</w:LidThemeOther>\n  <w:LidThemeAsian>X-NONE</w:LidThemeAsian>\n  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>\n  <w:Compatibility>\n   <w:BreakWrappedTables/>\n   <w:SnapToGridInCell/>\n   <w:WrapTextWithPunct/>\n   <w:UseAsianBreakRules/>\n   <w:DontGrowAutofit/>\n   <w:SplitPgBreakAndParaMark/>\n   <w:EnableOpenTypeKerning/>\n   <w:DontFlipMirrorIndents/>\n   <w:OverrideTableStyleHps/>\n  </w:Compatibility>\n  \n<w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel>\n  <m:mathPr>\n   <m:mathFont m:val=\"Cambria Math\"/>\n   <m:brkBin m:val=\"before\"/>\n   <m:brkBinSub m:val=\"&#45;-\"/>\n   <m:smallFrac m:val=\"off\"/>\n   <m:dispDef/>\n   <m:lMargin m:val=\"0\"/>\n   <m:rMargin m:val=\"0\"/>\n   <m:defJc m:val=\"centerGroup\"/>\n   <m:wrapIndent m:val=\"1440\"/>\n   <m:intLim m:val=\"subSup\"/>\n   <m:naryLim m:val=\"undOvr\"/>\n  </m:mathPr></w:WordDocument>\n</xml><![endif]--><!--[if gte mso 9]><xml>\n <w:LatentStyles DefLockedState=\"false\" DefUnhideWhenUsed=\"false\"\n  DefSemiHidden=\"false\" DefQFormat=\"false\" DefPriority=\"99\"\n  LatentStyleCount=\"371\">\n  <w:LsdException Locked=\"false\" Priority=\"0\" QFormat=\"true\" \nName=\"Normal\"/>\n  <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" \nName=\"heading 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 7\"/>\n  <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 8\"/>\n  <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 9\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index 4\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index 5\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index 6\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index 7\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index 8\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index 9\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"toc 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"toc 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"toc 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"toc 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"toc 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"toc 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"toc 7\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"toc 8\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"toc 9\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Normal Indent\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"footnote text\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"annotation text\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"header\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"footer\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"index heading\"/>\n  <w:LsdException Locked=\"false\" Priority=\"35\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"caption\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"table of figures\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"envelope address\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"envelope return\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"footnote reference\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"annotation reference\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"line number\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"page number\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"endnote reference\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"endnote text\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"table of authorities\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"macro\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"toa heading\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Bullet\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Number\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List 4\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List 5\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Bullet 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Bullet 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Bullet 4\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Bullet 5\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Number 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Number 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Number 4\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Number 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"10\" QFormat=\"true\" \nName=\"Title\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Closing\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Signature\"/>\n  <w:LsdException Locked=\"false\" Priority=\"1\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"Default Paragraph Font\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Body Text\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Body Text Indent\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Continue\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Continue 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Continue 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Continue 4\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"List Continue 5\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Message Header\"/>\n  <w:LsdException Locked=\"false\" Priority=\"11\" QFormat=\"true\" \nName=\"Subtitle\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Salutation\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Date\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Body Text First Indent\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Body Text First Indent 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Note Heading\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Body Text 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Body Text 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Body Text Indent 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Body Text Indent 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Block Text\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Hyperlink\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"FollowedHyperlink\"/>\n  <w:LsdException Locked=\"false\" Priority=\"22\" QFormat=\"true\" \nName=\"Strong\"/>\n  <w:LsdException Locked=\"false\" Priority=\"20\" QFormat=\"true\" \nName=\"Emphasis\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Document Map\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Plain Text\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"E-mail Signature\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Top of Form\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Bottom of Form\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Normal (Web)\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Acronym\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Address\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Cite\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Code\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Definition\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Keyboard\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Preformatted\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Sample\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Typewriter\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"HTML Variable\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Normal Table\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"annotation subject\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"No List\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Outline List 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Outline List 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Outline List 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Simple 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Simple 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Simple 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Classic 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Classic 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Classic 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Classic 4\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Colorful 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Colorful 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Colorful 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Columns 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Columns 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Columns 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Columns 4\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Columns 5\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Grid 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Grid 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Grid 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Grid 4\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Grid 5\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Grid 6\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Grid 7\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Grid 8\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table List 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table List 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table List 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table List 4\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table List 5\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table List 6\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table List 7\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table List 8\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table 3D effects 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table 3D effects 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table 3D effects 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Contemporary\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Elegant\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Professional\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Subtle 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Subtle 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Web 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Web 2\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Web 3\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Balloon Text\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"Table Grid\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nUnhideWhenUsed=\"true\"\n   Name=\"Table Theme\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" Name=\"Placeholder \nText\"/>\n  <w:LsdException Locked=\"false\" Priority=\"1\" QFormat=\"true\" Name=\"No\n Spacing\"/>\n  <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light \nShading\"/>\n  <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List\"/>\n  <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid\"/>\n  <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading \n1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading \n2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List \n1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List \n2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid \n1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid \n2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid \n3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List\"/>\n  <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful \nShading\"/>\n  <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful \nList\"/>\n  <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful \nGrid\"/>\n  <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1\n Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2\n Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" SemiHidden=\"true\" \nName=\"Revision\"/>\n  <w:LsdException Locked=\"false\" Priority=\"34\" QFormat=\"true\"\n   Name=\"List Paragraph\"/>\n  <w:LsdException Locked=\"false\" Priority=\"29\" QFormat=\"true\" \nName=\"Quote\"/>\n  <w:LsdException Locked=\"false\" Priority=\"30\" QFormat=\"true\"\n   Name=\"Intense Quote\"/>\n  <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent\n 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading\n Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1\n Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2\n Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent\n 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading\n Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1\n Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2\n Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent\n 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading\n Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1\n Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2\n Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent\n 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading\n Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1\n Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2\n Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent\n 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading\n Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1\n Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2\n Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent\n 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading\n Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"19\" QFormat=\"true\"\n   Name=\"Subtle Emphasis\"/>\n  <w:LsdException Locked=\"false\" Priority=\"21\" QFormat=\"true\"\n   Name=\"Intense Emphasis\"/>\n  <w:LsdException Locked=\"false\" Priority=\"31\" QFormat=\"true\"\n   Name=\"Subtle Reference\"/>\n  <w:LsdException Locked=\"false\" Priority=\"32\" QFormat=\"true\"\n   Name=\"Intense Reference\"/>\n  <w:LsdException Locked=\"false\" Priority=\"33\" QFormat=\"true\" \nName=\"Book Title\"/>\n  <w:LsdException Locked=\"false\" Priority=\"37\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" Name=\"Bibliography\"/>\n  <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\"\n   UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"TOC Heading\"/>\n  <w:LsdException Locked=\"false\" Priority=\"41\" Name=\"Plain Table \n1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"42\" Name=\"Plain Table \n2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"43\" Name=\"Plain Table \n3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"44\" Name=\"Plain Table \n4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"45\" Name=\"Plain Table \n5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"40\" Name=\"Grid Table \nLight\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"Grid Table 1 \nLight\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table \n2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table \n3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table \n4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 \nDark\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"Grid Table 6 \nColorful\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"Grid Table 7 \nColorful\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"Grid Table 1 Light Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 \nDark Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"Grid Table 6 Colorful Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"Grid Table 7 Colorful Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"Grid Table 1 Light Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 \nDark Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"Grid Table 6 Colorful Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"Grid Table 7 Colorful Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"Grid Table 1 Light Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 \nDark Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"Grid Table 6 Colorful Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"Grid Table 7 Colorful Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"Grid Table 1 Light Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 \nDark Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"Grid Table 6 Colorful Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"Grid Table 7 Colorful Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"Grid Table 1 Light Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 \nDark Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"Grid Table 6 Colorful Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"Grid Table 7 Colorful Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"Grid Table 1 Light Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 \nDark Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"Grid Table 6 Colorful Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"Grid Table 7 Colorful Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"List Table 1 \nLight\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table \n2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table \n3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table \n4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 \nDark\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"List Table 6 \nColorful\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"List Table 7 \nColorful\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"List Table 1 Light Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 \nAccent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 \nDark Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"List Table 6 Colorful Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"List Table 7 Colorful Accent 1\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"List Table 1 Light Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 \nAccent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 \nDark Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"List Table 6 Colorful Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"List Table 7 Colorful Accent 2\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"List Table 1 Light Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 \nAccent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 \nDark Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"List Table 6 Colorful Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"List Table 7 Colorful Accent 3\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"List Table 1 Light Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 \nAccent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 \nDark Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"List Table 6 Colorful Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"List Table 7 Colorful Accent 4\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"List Table 1 Light Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 \nAccent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 \nDark Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"List Table 6 Colorful Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"List Table 7 Colorful Accent 5\"/>\n  <w:LsdException Locked=\"false\" Priority=\"46\"\n   Name=\"List Table 1 Light Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 \nAccent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 \nDark Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"51\"\n   Name=\"List Table 6 Colorful Accent 6\"/>\n  <w:LsdException Locked=\"false\" Priority=\"52\"\n   Name=\"List Table 7 Colorful Accent 6\"/>\n </w:LatentStyles>\n</xml><![endif]--><!--[if gte mso 10]>\n<style>\n /* Style Definitions */\n table.MsoNormalTable\n\t{mso-style-name:\"Table Normal\";\n\tmso-tstyle-rowband-size:0;\n\tmso-tstyle-colband-size:0;\n\tmso-style-noshow:yes;\n\tmso-style-priority:99;\n\tmso-style-parent:\"\";\n\tmso-padding-alt:0in 5.4pt 0in 5.4pt;\n\tmso-para-margin:0in;\n\tmso-para-margin-bottom:.0001pt;\n\tmso-pagination:widow-orphan;\n\tfont-size:10.0pt;\n\tfont-family:\"Times New Roman\",serif;}\n</style>\n<![endif]-->days.<br><br>Your payment in the amount \nof&nbsp;<b><font size=\"4\" color=\"#004993\">${data.amountDue:de}</font></b>&nbsp;is due \non or before&nbsp;<font size=\"4\" color=\"#004993\" style=\"\"><b>{data.holdExpDate:dt}</b></font>.<br><br>Please\n ensure you allow sufficient time for your payment to be received on or \nbefore {data.holdExpDate:dt}.&nbsp; If you are concerned about your payment\n posting on time, we offer several options to pay your bill that post to\n your account immediately! Click&nbsp;<a href=\"https://www.consumersenergy.com/residential/billing-and-payment/payment\" target=\"_blank\">here</a>&nbsp;to learn \nmore.<br><br>Sincerely,<br><br>Consumers \nEnergy<br><br><br>Please do not reply to this \nautomated email message. </td>\n        <td><br></td>\n     </tr>\n</tbody></table> ", "isActive": true, "version": "********-112317", "updatedAt": {"$date": "2022-12-12T20:22:14.966Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62da87c9788b926316ac3de9"}, "templateName": "sms-csr-shutoff-hold-spp", "templateHtml": "Consumers Energy: CONFIRMATION OF SPP ENROLLMENT REQUEST\nAccount: {serviceAddress}\nDown Payment Amount: ${data.amountDue:de}\nDown Payment Due Date: {data.holdExpDate:dt}\nReason for Hold: Pending Shut-Off Protection Plan Enrollment Request\n\nTo Pay Online Now, visit: https://www.consumersenergy.com/residential/billing-and-payment/payment", "isActive": true, "version": "********-112338", "updatedAt": {"$date": "2022-12-12T20:22:14.970Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62da5215aa54cf791f1e80d5"}, "templateName": "email-csr-care-ser", "templateHtml": "<img src=\"app-api/images/stored/top-tsohc.png\"><br><div><table><tbody><tr><td>Account Number:</td><td>ending in {accountNumber:last-chars:4}</td></tr><tr><td>Service Address:</td><td>{serviceAddress}<br></td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><b><font size=\"4\" color=\"#004993\"><br>Dear {firstName} {lastName},</font></b><br><br>Thank you for calling Consumers Energy on {currentDate:dt} to make a payment arrangement on your past due account. We’ve completed your request for a {data.holdDays}-day temporary shut-off hold to apply for assistance with {data.agency.name}.<br><br>Your shut-off hold will expire on&nbsp;<font size=\"4\" color=\"#004993\" style=\"\"><b>{data.holdExpDate:dt}</b></font>.<br><br><div><div>Once {data.agency.name}&nbsp;receives your application, they will contact us to extend the hold while your application is being reviewed. Don’t delay and begin the application process now by{data.agency.website:start-check-exists} going to <b><a href=\"{data.agency.website}\">{data.agency.website}</a></b>{data.agency.website:end-check-exists}<span style=\"background-color: transparent;\">{data.agency.phone:start-check-exists}</span><span style=\"background-color: transparent;\">{data.agency.website:start-check-exists}&nbsp;</span><span style=\"background-color: transparent;\">or</span><span style=\"background-color: transparent;\">{data.agency.website:end-check-exists}</span><span style=\"background-color: transparent;\">{data.agency.phone:end-check-exists}</span><span style=\"background-color: transparent;\">{data.agency.phone:start-check-exists}</span><span style=\"background-color: transparent;\">&nbsp;calling </span><b style=\"background-color: transparent;\">{data.agency.phone}</b><span style=\"background-color: transparent;\">{data.agency.phone:end-check-exists}.</span></div><div><br></div><div>To apply, you will need the following supporting documents:</div></div><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><div><div><font size=\"5\" color=\"#004993\">\n❶</font>\nMost recent Consumers Energy bill</div></div><div><div><font size=\"5\" color=\"#004993\">\n❷</font>\nSocial Security card of applicant and social security numbers for household members</div></div><div><div><font size=\"5\" color=\"#004993\">\n❸</font>\nDriver's license of applicant</div></div><div><div><font size=\"5\" color=\"#004993\">❹</font>Proof of all household gross income (amount before deductions) received during the 30 days prior to the application signature date&nbsp;</div></div></blockquote><div><div><br></div><div>You may be eligible for more than one program! Click&nbsp;<a href=\"https://www.consumersenergy.com/residential/programs-and-services/payment-assistance#AssistanceWeOffer\" target=\"_blank\">here</a>&nbsp;to learn more about assistance options available for qualifying customers.</div></div><div><br></div><div><div>Sincerely,<br>Consumers Energy<br><br>Please do not reply to this automated email message.<br></div><div><br></div><div><br></div></div></td><td><br></td></tr></tbody></table><br> </div>", "isActive": true, "version": "********-110048", "updatedAt": {"$date": "2022-12-12T20:22:14.972Z"}, "createdAt": {"$date": "2022-08-11T16:50:09.497Z"}, "__v": 0}, {"_id": {"$oid": "62da4e6aaa54cf791f1e7fff"}, "templateName": "sms-csr-care-ser", "templateHtml": "Consumers Energy: CONFIRMATION OF TEMPORARY SHUT-OFF HOLD\nAccount: {serviceAddress}\nHold Expiration Date: {data.holdExpDate:dt}\nAgency Contact Information:\n{data.agency.name}\n{data.agency.website:start-check-exists}{data.agency.website}{data.agency.website:end-check-exists}{data.agency.website:start-check-exists}{data.agency.phone:start-check-exists} or {data.agency.phone:end-check-exists}{data.agency.website:end-check-exists}{data.agency.phone:start-check-exists}{data.agency.phone}{data.agency.phone:end-check-exists}\nGo to https://www.consumersenergy.com/residential/programs-and-services/payment-assistance#AssistanceWeOffer to learn more about other energy assistance available to you.", "isActive": true, "version": "********-110438", "updatedAt": {"$date": "2022-12-12T20:22:14.975Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62da4dc8aa54cf791f1e7fee"}, "templateName": "sms-csr-shutoff-hold-sph", "templateHtml": "Consumers Energy: PAYMENT ARRANGEMENT CONFIRMATION\n\nAccount: {serviceAddress}\nShut-off Amount: ${data.amountDue:de}\nHold Expiration Date: {data.holdExpDate:dt}\n\nPay Online Now by going to https://www.consumersenergy.com/residential/billing-and-payment/payment", "isActive": true, "version": "********-071246", "updatedAt": {"$date": "2022-12-12T20:22:14.977Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62da4afdaa54cf791f1e7ee6"}, "templateName": "email-csr-shutoff-hold-sph", "templateHtml": "<img src=\"app-api/images/stored/top-pac.png\"><table> <tbody><tr><td>Account Number:</td><td>ending in&nbsp;{accountNumber:last-chars:4}</td></tr> <tr><td>Service Address:</td><td>{serviceAddress}</td></tr> </tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br><br>Thank you for calling Consumers Energy on {currentDate:dt} to make a payment arrangement on your past due balance. We’ve completed your request for additional time to pay your bill and have placed a {data.holdDays}-day {data.serviceOfferingTitle} extension on your account.<br> <br> Your payment in the amount of <b><font size=\"4\" color=\"#004993\"> ${data.amountDue:de}</font></b> is due on or before <font size=\"4\" color=\"#004993\" style=\"background-color: transparent;\"><b>{data.holdExpDate:dt}</b></font><span style=\"background-color: transparent;\">.</span><font size=\"4\" color=\"#004993\" style=\"\"><b><br></b></font> <br>Please ensure you allow sufficient time for your payment to be received on or before {data.holdExpDate:dt}.&nbsp; If you are concerned about your payment posting on time, we offer several options to pay your bill that post to your account immediately! Click&nbsp;<a href=\"https://www.consumersenergy.com/residential/billing-and-payment/payment\" target=\"_blank\" style=\"background-color: transparent;\">here</a><span style=\"background-color: transparent;\"> to learn more.</span><br> <br> Sincerely,<br><br> Consumers Energy<br><br><br>Please do not reply to this automated email message.</td><td><br></td></tr></tbody></table> &nbsp;", "isActive": true, "version": "********-151928", "updatedAt": {"$date": "2024-09-20T15:19:28.640Z"}, "createdAt": {"$date": "2022-07-28T18:00:57.085Z"}, "__v": 0}, {"_id": {"$oid": "62cf48d9a31d5efef71629aa"}, "templateName": "sms-rev-medical-letter", "templateHtml": "Here is the link to the Medical Certification Form you requested:\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/2330\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-204122", "updatedAt": {"$date": "2022-12-12T20:22:14.985Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62cf48c0a31d5efef71629a5"}, "templateName": "email-rev-medical-letter", "templateHtml": "<img src=\"app-api/images/stored/top_mcf.jpg\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;&nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br></div><div><div>Here is the link to the Medical Certification Form you requested:</div><div><br></div><div><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/2330\" target=\"_blank\">https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/2330</a></div></div><div><br></div><div><div>Sincerely,</div><div><br></div><div>Consumers Energy</div><div><br></div><div>Please do not reply to this automated message.</div></div>", "isActive": true, "version": "********-204144", "updatedAt": {"$date": "2022-12-12T20:22:14.988Z"}, "createdAt": {"$date": "2022-08-04T19:14:15.715Z"}, "__v": 0}, {"_id": {"$oid": "62b209d3a7a0268263edb8e0"}, "templateName": "sms-dc-damage-claims", "templateHtml": "Consumers Energy Damage Claims Notification # {dcNotifyNumber}\n\nThank you for reaching out to Consumers Energy. If you need immediate assistance, please work with your insurance provider. Your claim has been submitted and is currently being investigated to determine liability. You will receive communication from Consumers Energy within 3-5 business days.\n\n\nThank you for being a valued Consumers Energy customer.\n", "isActive": true, "version": "********-193357", "updatedAt": {"$date": "2022-12-12T20:22:14.990Z"}, "createdAt": {"$date": "2022-07-18T17:14:13.830Z"}, "__v": 0}, {"_id": {"$oid": "62b1d4efa255c9497cb2279a"}, "templateName": "email-dc-damage-claims", "templateHtml": "<img src=\"app-api/images/stored/damage-claim.png\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; {account-number}<br>Service Address:&nbsp; &nbsp;&nbsp;{service-address}<br></td><td><br></td><td></td></tr> </tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br>Damage Claim - Order Confirmation for Damage Claim<br><br>Thank you for reaching out to Consumers Energy. If you need immediate assistance, please work with your insurance provider. Your claim has been submitted and is currently being investigated to determine liability. You will receive communication from Consumers Energy within 3-5 business days.<br><br>Thank you for being a valued Consumers Energy customer,<br><br><img src=\"app-api/images/stored/CE_signatureLogo.jpg\"><br>This response is from an unmonitored email. Please do not respond.<br><br></td><td><br></td></tr></tbody></table> &nbsp;", "isActive": true, "version": "********-204606", "updatedAt": {"$date": "2022-12-12T20:22:14.992Z"}, "createdAt": {"$date": "2022-07-21T22:56:22.949Z"}, "__v": 0}, {"_id": {"$oid": "62aa5ac67f9ff6257143bb99"}, "templateName": "sms-eo-shutoff-notice", "templateHtml": "DECREASE ELECTRIC LOAD TO AVOID SHUT OFF\n\nUnsafe electric use was discovered at this property, please decrease your electric load to avoid shut off.\n\nUnder Consumers Energy’s terms of service, we are legally authorized to shut off electrical service to a customer’s property if a hazardous situation is present. We’ve determined the electric load at your property exceeds the safe operating limit of our equipment, and thus creates a hazardous situation.\n\nFor safety reasons, your electric load must be permanently reduced within 24 hours. If you fail to comply, your electric service will be shut off until electric equipment can be upgraded or a plan to reduce load is approved. If shut off occurs, proof of an electrical inspection by a local electrical inspector will also be required before service is restored.\n\nTo request an upgrade and begin the electric service restoration process, please contact us as soon as possible by calling ************. Refer to Order Number: {data.orderNumber}\n\nThis response is from an unmonitored account. Please do not respond.", "isActive": true, "version": "********-224722", "updatedAt": {"$date": "2022-12-12T20:22:14.995Z"}, "createdAt": {"$date": "2022-07-08T16:18:25.080Z"}, "__v": 0}, {"_id": {"$oid": "62a7a5bb2f94f2e465624523"}, "templateName": "email-eo-shutoff-notice", "templateHtml": "<img src=\"app-api/images/stored/top_eo.jpg\"><br><table> <tbody><tr><td><br></td><td></td></tr><tr><td></td><td><br><br></td></tr> </tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><table style=\"width: 1110.67px;\"><tbody><tr><td>Account Number:&nbsp; ending in {accountNumber:last-chars:4}</td><td><br></td></tr><tr><td><br>Service Address:&nbsp; &nbsp;&nbsp;{serviceAddress}<br></td><td></td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\" style=\"width: 1110.67px;\"><tbody><tr></tr></tbody></table><br><b><font size=\"4\" color=\"#004993\">Dear Neighbor,</font></b><br><br>Unsafe electric use was discovered at this property, please decrease your electric load to avoid shut off.<br><br>Under Consumers Energy’s terms of&nbsp; service, we are legally authorized to shut off electrical service to a customer’s property if a hazardous situation is present. We’ve determined the electric load at your property exceeds the safe operating limit of our equipment, and thus creates a hazardous situation.<br><br>For safety reasons, your electric load must be permanently reduced within 24 hours. If you fail to&nbsp; comply, your electric service will be shut off until electric equipment can be upgraded or a plan to reduce load is approved. If shut off occurs, proof of an electrical inspection by a local electrical inspector will also be required before service is restored.<br><br>To request an upgrade and begin the electric service restoration process, please contact us as soon as possible by calling ************. Refer to Order Number:&nbsp; {data.orderNumber}<br><br>Sincerely,<br><br>Consumers Energy<br><br>Please do not reply to this automated email message.<br><br><br></td><td><br></td></tr></tbody></table> &nbsp;", "isActive": true, "version": "********-223000", "updatedAt": {"$date": "2022-12-12T20:22:14.998Z"}, "createdAt": {"$date": "2022-07-21T22:56:18.254Z"}, "__v": 0}, {"_id": {"$oid": "633749d41e1960a2eae5553c"}, "templateName": "email-dpo-cash-receipt-nite-drop", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div><div><font face=\"Arial\">We ask that you no longer deposit cash into the payment drop box. The box is for non-cash payments only.&nbsp;</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">The after-hours payment drop box is provided for our customers’ convenience.&nbsp; To ensure the accuracy of amounts paid, all future payments placed in the after-hours payment drop box should be made with a check or money order only.&nbsp;&nbsp;</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\"><div>Payment Amount: ${data.dropPaymentAmount:de}</div><div><br></div><div>Payment Date: {data.dropPaymentDate:dt:MM/DD/YYYY}</div><div><br></div></font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">We would like to thank you for using our after-hours payment drop box.&nbsp; If you have any questions or concerns, please call our Customer Service Center at **************.</font></div></div><div><div><br></div><div>&nbsp;</div><div><div>K. Fick</div><div><br></div><div>Direct Payment Office Supervisor</div><div><br></div><div>&nbsp;</div><div>Please do not reply to this automated message.</div></div></div></div><div><br></div></div>", "isActive": true, "version": "********-212444", "updatedAt": {"$date": "2022-12-12T20:22:15.001Z"}, "createdAt": {"$date": "2022-09-30T19:56:04.240Z"}, "__v": 0}, {"_id": {"$oid": "625de23f766c612b06645138"}, "templateName": "sms-dpo-cash-receipt-nite-drop", "templateHtml": "Consumers Energy Direct Payment Office\n\nWe ask that you no longer deposit cash into the payment drop box. The box is for non-cash payments only. \n\nThe after-hours payment drop box is provided for our customers’ convenience.  To ensure the accuracy of amounts paid, all future payments placed in the after-hours payment drop box should be made with a check or money order only.  \n\n\nAccount Number:  ending in {accountNumber:last-chars:4}\n\nPayment Amount: ${data.dropPaymentAmount:de}\n\nPayment Date: {data.dropPaymentDate:dt:MM/DD/YYYY}\n\n\nWe would like to thank you for using our after-hours payment drop box.  If you have any questions or concerns, please call our Customer Service Center at **************.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-220550", "updatedAt": {"$date": "2022-12-12T20:22:15.003Z"}, "createdAt": {"$date": "2022-04-18T22:12:15.044Z"}, "__v": 0}, {"_id": {"$oid": "633cb95d723592b90f823eb8"}, "templateName": "sms-dpo-payment-receipt", "templateHtml": "Consumers Energy Direct Payment Office\n\nPlease click on the link to view your payment receipt. \n{url}\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-225317", "updatedAt": {"$date": "2022-12-12T20:22:15.006Z"}, "createdAt": {"$date": "2022-10-04T22:53:17.147Z"}, "__v": 0}, {"_id": {"$oid": "633c4cf8ed5598ad4dd698eb"}, "templateName": "email-dpo-payment-receipt", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\">\n\n<br><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br>\n    <br><b>Payment Receipt</b><br><br><br>Date: {currentDate:dt:MM/DD/YYYY}<br>Cash Desk: {data.cashDesk}<br>\n    <div style=\"min-width: auto;margin-top: 20px;margin-left: 50px;\">\n\n          <div>\n      <span style=\"display: inline-block;background-color: #004993;color: white;font-size: 12px;width: 120px;font-weight: bold;\">Account #</span>\n      <span style=\"display: inline-block;background-color: #004993;color: white;font-size: 12px;width: 70px;font-weight: bold;\">Name</span>\n<span style=\"display: inline-block;background-color: #004993;color: white;font-size: 12px;width: 115px;font-weight: bold;\">Amount Paid</span>\n      \n    </div>\n\n      {data.accounts:start-loop}\n\n\n     <div><span style=\"display: inline-block;background-color: #ccffcc;font-size: 12px;width: 120px;\">{accountNbr}</span>\n      <span style=\"display: inline-block;background-color: #ccffcc;font-size: 12px;width: 70px;\">{fullName}</span>\n<span style=\"display: inline-block;background-color: #ccffcc;font-size: 12px;width: 115px;\">${amountPd:de}</span>\n      \n</div>\n      {data.accounts:end-loop}\n    </div>\n    <br>Number of Accounts: {data.numberAccounts}<br>Payment Type: {data.receivedVia}<br>Amount Received: ${data.amtReceived:de}<br>Amount Applied: ${data.amtApplied:de}<br>Change: ${data.changeRet:de}<br><br><br>Please do not reply to this automated message.<br>\n  </td>\n\n  <td>\n    <br>\n  </td>\n</tr>\n</tbody>\n</table>\n\n", "isActive": true, "version": "********-211617", "updatedAt": {"$date": "2022-12-12T20:22:15.008Z"}, "createdAt": {"$date": "2022-10-04T15:10:48.420Z"}, "__v": 0}, {"_id": {"$oid": "63471e3055051252ff2918c9"}, "templateName": "sms-dpo-docs-received", "templateHtml": "Consumers Energy Direct Payment Office\n\nThank you.  We have received the following document(s):\n\n{data.newServiceRequest:start-check-exists}\n{data.newServiceRequest:check} New Service Request {data.newServiceRequest:end-check-exists} {data.powerOfAttorney:start-check-exists}\n{data.powerOfAttorney:check} Power of Attorney {data.powerOfAttorney:end-check-exists}{data.deathCertificateEstateRefund:start-check-exists}\n{data.deathCertificateEstateRefund:check} Death Certificate/Estate Refund Check {data.deathCertificateEstateRefund:end-check-exists}{data.applianceRepairRebate:start-check-exists}\n{data.applianceRepairRebate:check} Appliance Repair/Rebate {data.applianceRepairRebate:end-check-exists}{data.identityTheftReport:start-check-exists}\n{data.identityTheftReport:check} Identity Theft Report {data.identityTheftReport:end-check-exists} {data.guardianConservator:start-check-exists}\n{data.guardianConservator:check} Guardianship/Conservatorship{data.guardianConservator:end-check-exists}{data.medicalEmergencyForms:start-check-exists}\n{data.medicalEmergencyForms:check} Medical Emergency Forms {data.medicalEmergencyForms:end-check-exists}{data.affidavitReturnChecks:start-check-exists}\n{data.affidavitReturnChecks:check} Affidavit For Return Checks {data.affidavitReturnChecks:end-check-exists}\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-211035", "updatedAt": {"$date": "2022-12-12T20:22:15.015Z"}, "createdAt": {"$date": "2022-10-12T20:06:08.348Z"}, "__v": 0}, {"_id": {"$oid": "634717ea55051252ff2918ba"}, "templateName": "email-dpo-docs-received", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div><div><font face=\"Arial\">Thank you.&nbsp; We have received the following document(s):</font><br></div></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\"><div>{data.newServiceRequest:start-check-exists}</div><div><b>{data.newServiceRequest:check} New Service Request</b></div><div>{data.newServiceRequest:end-check-exists}</div><div>{data.powerOfAttorney:start-check-exists}</div><div><b>{data.powerOfAttorney:check} Power of Attorney</b></div><div>{data.powerOfAttorney:end-check-exists}</div><div>{data.deathCertificateEstateRefund:start-check-exists}</div><div><b>{data.deathCertificateEstateRefund:check} Death Certificate/Estate Refund Check</b></div><div>{data.deathCertificateEstateRefund:end-check-exists}</div><div>{data.applianceRepairRebate:start-check-exists}</div><div><b>{data.applianceRepairRebate:check} Appliance Repair/Rebate</b></div><div>{data.applianceRepairRebate:end-check-exists}</div><div>{data.identityTheftReport:start-check-exists}</div><div><b>{data.identityTheftReport:check} Identity Theft Report</b></div><div>{data.identityTheftReport:end-check-exists}</div><div>{data.guardianConservator:start-check-exists}</div><div><b>{data.guardianConservator:check} Guardianship/Conservatorship</b></div><div>{data.guardianConservator:end-check-exists}</div><div>{data.medicalEmergencyForms:start-check-exists}</div><div><b>{data.medicalEmergencyForms:check} Medical Emergency Forms</b></div><div>{data.medicalEmergencyForms:end-check-exists}</div><div>{data.affidavitReturnChecks:start-check-exists}</div><div><b>{data.affidavitReturnChecks:check} Affidavit For Return Checks</b></div><div>{data.affidavitReturnChecks:end-check-exists}</div><div><br></div></font></div><div><div>&nbsp;</div><div><p data-renderer-start-pos=\"401\" style=\"margin-top: 0.75rem; margin-right: 0px; margin-left: 0px; padding: 0px; font-size: 16px; line-height: 1.714; letter-spacing: -0.005em; box-sizing: border-box; color: rgb(23, 43, 77); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; white-space: pre-wrap;\">Sincerely,</p><p data-renderer-start-pos=\"413\" style=\"margin-top: 0.75rem; margin-right: 0px; margin-left: 0px; padding: 0px; font-size: 16px; line-height: 1.714; letter-spacing: -0.005em; box-sizing: border-box; color: rgb(23, 43, 77); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; white-space: pre-wrap;\">Consumers Energy</p><p data-renderer-start-pos=\"431\" style=\"margin-top: 0.75rem; margin-right: 0px; margin-left: 0px; padding: 0px; font-size: 16px; line-height: 1.714; letter-spacing: -0.005em; box-sizing: border-box; color: rgb(23, 43, 77); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; white-space: pre-wrap;\">Please do not reply to this automated message.</p></div></div></div><div><br></div></div>", "isActive": true, "version": "********-211101", "updatedAt": {"$date": "2022-12-12T20:22:15.018Z"}, "createdAt": {"$date": "2022-10-12T19:39:22.403Z"}, "__v": 0}, {"_id": {"$oid": "6349e40e2b128c18418a8282"}, "templateName": "sms-dpo-security-dep-receipts", "templateHtml": "Consumers Energy Direct Payment Office\n\nPlease click on the following link to view your security deposit receipt: \n\n{url}\n\nIf you have any questions or concerns, please call our customer service center toll free at (800)477-5050. One of our customer service representatives will be glad to help.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-162602", "updatedAt": {"$date": "2022-12-12T20:22:15.021Z"}, "createdAt": {"$date": "2022-10-14T22:34:54.062Z"}, "__v": 0}, {"_id": {"$oid": "6349da9d1af7d24de7d48d6e"}, "templateName": "email-dpo-security-dep-receipts", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><div style=\"text-align: center;\"><font color=\"#000430\"><b>CERTIFICATE FOR SECURITY DEPOSIT</b></font></div><div style=\"text-align: center;\"><font color=\"#000430\"><b><br></b></font></div><div style=\"text-align: center;\"><font color=\"#000430\"><b>(NOT NEGOTIABLE OR TRANSFERABLE)</b></font></div></div><div><font color=\"#000430\"><b><br></b></font></div><div><div><font face=\"Arial\"><br></font></div><div><div><font face=\"Arial\">On {data.depositReceivedDate:dt:MM/DD/YYYY}, Consumers Energy received a deposit of ${data.depositAmount:de}&nbsp;for the above account. The security deposit may be applied to any amount owed to the company for energy service. Consumers Energy reserves the right to discontinue energy service at the above address if payments are not received and not to apply the security deposit until after service is discontinued.</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">Consumers Energy will credit interest at a rate prescribed by the Michigan Public Service Commission (MPSC) to the above energy account or include interest when the deposit is returned. Current interest rates are available at a Consumers Energy office or online at the MPSC website: <a href=\"\\&quot;https://www.michigan.gov/mpsc\" target=\"\\&quot;_blank\\&quot;\">www.michigan.gov/mpsc</a>. To access the rates, select Consumer Info on the home page, then \"Electric or Gas Customers and Residential Billing Rules.\"</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">We will refund deposits and earned interest after satisfactory payment of all proper charges for up to&nbsp;</font><span style=\"font-family: Arial; background-color: transparent;\">{data.dpoSecMonths}</span><span style=\"font-family: Arial; background-color: transparent;\">&nbsp;consecutive months. We may retain the deposit for unauthorized use, diversion, or interference of energy services for up to 36 months and refund the deposit after satisfactory payment of up to the final&nbsp;</span><span style=\"font-family: Arial;\">{data.dpoSecMonths}&nbsp;</span><span style=\"font-family: Arial; background-color: transparent;\">&nbsp;months charges.</span></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">Consumers Energy will refund deposits and earned interest by making a credit to the active service account of the customer. Payments are satisfactory if made before Consumers Energy issues a notice that service will be discontinued for nonpayment not in dispute or within five days after the next succeeding monthly bill is issued, whichever is sooner. For more details, please see Residential R 460.111 and Non-Residential R 460.1607 on the Residential Billing Rules page at the MPSC website: <a href=\"\\&quot;https://www.michigan.gov/mpsc\" target=\"\\&quot;_blank\\&quot;\">www.michigan.gov/mpsc</a>.</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">If you have any questions or concerns, please call our customer service center toll free at (800)477-5050. One of our customer service representatives will be glad to help.</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">&nbsp;</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">Customer Service</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\"><a href=\"\\&quot;https://www.consumersenergy.com\" target=\"\\&quot;_blank\\&quot;\">www.consumersenergy.com</a></font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">Please do not reply to this automated message.<br></font></div></div></div><div><font face=\"Arial\"><br></font></div>", "isActive": true, "version": "20221014-223247", "updatedAt": {"$date": "2022-12-12T20:22:15.024Z"}, "createdAt": {"$date": "2022-10-14T21:54:37.103Z"}, "__v": 0}, {"_id": {"$oid": "634f30c674327b774030a995"}, "templateName": "sms-dpo-10k-letter", "templateHtml": "Consumers Energy Direct Payment Office\n\nDue to a requirement of the U.S. Department of Treasury, Consumers Energy must report all cash payments received in the last 12-month period that cumulatively total over $10,000. \n\nPlease click on the link to view your payment history and follow the instructions provided.\n\n{url}\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-162340", "updatedAt": {"$date": "2022-12-12T20:22:15.027Z"}, "createdAt": {"$date": "2022-10-18T23:03:34.533Z"}, "__v": 0}, {"_id": {"$oid": "634f18c83f1dd6780f7eaf96"}, "templateName": "email-dpo-10k-letter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div><div><font face=\"Arial\">Due to a requirement of the U.S. Department of Treasury, Consumers Energy must report all cash payments received in the last 12-month period that cumulatively total over $10,000. Our records indicate the following cash payment(s) were received:</font><br></div></div>\n    <div style=\"min-width: auto;margin-top: 20px;margin-left: 50px;\">\n\n          <div>\n      <span style=\"display: inline-block;background-color: #004993;color: white;font-size: 12px;width: 120px;font-weight: bold;\">Account #</span>\n<span style=\"display: inline-block;background-color: #004993;color: white;font-size: 12px;width: 120px;font-weight: bold;\">Payment Amount</span>&nbsp;<span style=\"display: inline-block;background-color: #004993;color: white;font-size: 12px;width: 120px;font-weight: bold;\">Payment Date</span>\n    </div>\n\n      {data.cashPayments:start-loop}\n\n\n     <div><span style=\"display: inline-block;background-color: #ccffcc;font-size: 12px;width: 120px;\">{accountNbrCash}</span>\n      \n<span style=\"display: inline-block;background-color: #ccffcc;font-size: 12px;width: 120px;\">${paymentAmt:de}</span>\n      <span style=\"display: inline-block;background-color: #ccffcc;font-size: 12px;width: 120px;\">{paymentDate:dt}</span>\n</div>\n      {data.cashPayments:end-loop}\n    </div>\n    <br><div>Please complete and return the following documentation in the self-addressed envelope within 30 days of the date of this letter:</div><div><br></div><div>Form 1830 -&nbsp;<a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1830\" target=\"_blank\" style=\"background-color: transparent;\">https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1830</a></div><div><br></div><div>Copies of the requested identification documents (driver’s license, passport, alien registration card, etc.)&nbsp;</div><div><br></div><div>Consumers Energy appreciates your assistance in providing the requested information. Failure or refusal to comply may result in civil penalties of up to $270.00 as well as potential criminal penalties.</div><div><br></div><div>&nbsp;</div><div><br></div><div>Consumers Energy</div><div><br></div><div>Corporate Tax</div><div><br></div><div>************</div><div><br></div><div>&nbsp;</div><div><br></div><div>Please do not reply to this automated message.</div><div><br></div>\n  \n\n  \n    <br>\n  \n\n\n\n\n</div></div>", "isActive": true, "version": "********-162437", "updatedAt": {"$date": "2022-12-12T20:22:15.029Z"}, "createdAt": {"$date": "2022-10-18T21:21:12.698Z"}, "__v": 0}, {"_id": {"$oid": "61d4e10aa2f90c937d05e68b"}, "templateName": "email-tpa-template", "templateHtml": "<div style=\"width: 960px;\">\n\t<br><table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_tpn.jpg\" width=\"960\" height=\"238\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>Thank you for calling Consumers Energy to request a {service-offering-title} form for service address of {service-address}.</p><p>Please click the link below to continue with your request and view documentation required.&nbsp;</p><p>Visit:</p><p><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1394.ashx\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1394.ashx</a><br>&nbsp;</p><p>This form requires the following steps:</p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><font size=\"5\" color=\"#004993\">❶</font>&nbsp;<span style=\"background-color: transparent;\">Fill out all required fields. All fields are required unless otherwise indicated. (Note: The&nbsp;</span>{service-offering-title}<span style=\"background-color: transparent;\">&nbsp;form needs to be filled out by both the Consumers Energy customer and the Third Party intended to receive notification)<br></span><font size=\"5\" color=\"#004993\">❷</font>&nbsp;<span style=\"background-color: transparent;\">Once form is completed, please either email, fax, or mail the form to:</span></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><span style=\"background-color: transparent;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b>Email:&nbsp;</b><a href=\"mailto:<EMAIL>\" style=\"background-color: transparent;\"><EMAIL></a></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b>Fax:&nbsp;</b><span style=\"background-color: transparent;\">517-325-8221</span></blockquote></span><span style=\"background-color: transparent;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b>Mail:</b></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\">Lansing Consumer Affairs, Room 214<br>Consumers Energy<br>PO Box 30162<br>Lansing, MI 48909-7662<br></blockquote></span></blockquote>Once all required documentation has been received, your request will be completed within 10 business days. If you have any questions, please contact us at ************.<br><br><br>Sincerely,<br>Consumers Energy<br><p>Please do not reply to this automated email message</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "20220112-162307", "updatedAt": {"$date": "2022-12-12T20:22:15.033Z"}, "createdAt": {"$date": "2022-01-14T21:02:44.392Z"}, "__v": 0}, {"_id": {"$oid": "61d4e10aa2f90c937d05e68a"}, "templateName": "sms-tpa-template", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {service-offering-title} Form. Please click the link below to continue with your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1394.ashx", "isActive": true, "version": "20220112-162307", "updatedAt": {"$date": "2022-12-12T20:22:15.036Z"}, "createdAt": {"$date": "2022-01-14T21:02:44.392Z"}, "__v": 0}, {"_id": {"$oid": "61d4e10aa2f90c937d05e689"}, "templateName": "email-roci-template", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_roci.jpg\" width=\"960\" height=\"238\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>Thank you for calling Consumers Energy to request a {service-offering-title} form for service address of {service-address}.</p><p>Please click the link below to continue with your request and view documentation required.&nbsp;</p><p>Visit:</p><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1619.ashx\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1619.ashx</a><br><p>Note: The {service-offering-title} form is used for <u>residential accounts</u>.&nbsp;</p><p>This form requires the following steps:</p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><font size=\"5\" color=\"#004993\">❶</font>&nbsp;<span style=\"background-color: transparent;\">Fill out all required fields. All fields are required unless otherwise indicated. (Note: You are able to give authorization on multiple addresses or account numbers)<br></span><font size=\"5\" color=\"#004993\">❷</font>&nbsp;<span style=\"background-color: transparent;\">Once form is completed, please either email or mail the form to:</span></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><span style=\"background-color: transparent;\">Consumers Energy Customer Service&nbsp;</span><br>4000 Clay Avenue SW&nbsp;<br>Grand Rapids, MI 49548-301&nbsp;<br><a href=\"mailto:<EMAIL>\"><EMAIL></a></blockquote></span></blockquote>You may also take the form into any Consumers Energy Direct Payment Office.<br><br>Once all required documentation has been received, your request will be completed within 10 business days. If you have any questions, please contact us at ************.<br><br>Sincerely,<br>Consumers Energy<br><p>Please do not reply to this automated email message</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "20220112-162307", "updatedAt": {"$date": "2022-12-12T20:22:15.038Z"}, "createdAt": {"$date": "2022-01-14T21:02:44.392Z"}, "__v": 0}, {"_id": {"$oid": "61d4e10aa2f90c937d05e688"}, "templateName": "sms-roci-template", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {service-offering-title} Form. Please click the link below to continue with your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/Customer%20Forms/1619.ashx", "isActive": true, "version": "20220112-162307", "updatedAt": {"$date": "2022-12-12T20:22:15.041Z"}, "createdAt": {"$date": "2022-01-14T21:02:44.392Z"}, "__v": 0}, {"_id": {"$oid": "61d4d2dda2f90c937d05e685"}, "templateName": "email-loa-template", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_loa.jpg\" width=\"960\" height=\"238\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>Thank you for calling Consumers Energy to request a {service-offering-title} for service address of {service-address}.</p><p><span style=\"background-color: transparent;\">Please click the link below to continue with your request and view documentation required.&nbsp;</span><br></p><p><span style=\"background-color: transparent;\">Visit:</span><br></p><p><span style=\"background-color: transparent;\"><a href=\"https://www.consumersenergy.com/-/media/CE/Documents/business/letter-of-authorization-business-customers.ashx\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.consumersenergy.com/-/media/CE/Documents/business/letter-of-authorization-business-customers.ashx</a><br></span><br></p><p><span style=\"background-color: transparent;\">Note: The Letter of Authorization is used for business customers set up as an organization. This form gives authorization for one year.</span><br></p><p><span style=\"background-color: transparent;\">This form requires the following steps:</span><br></p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><font size=\"5\" color=\"#004993\">❶</font>&nbsp;<span style=\"background-color: transparent;\">Fill out all required fields. All fields are required unless otherwise indicated. (Note: You are able to give authorization on multiple addresses or account numbers)<br></span><font size=\"5\" color=\"#004993\">❷</font>&nbsp;<span style=\"background-color: transparent;\">Once form is completed, please either email or mail the form to:</span></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><span style=\"background-color: transparent;\"><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\">LANDLORD &amp; SMALL BUSINESS TEAM - ROOM 214<br>C/O CONSUMERS ENERGY<br>4000 CLAY AVENUE SW<br>GRAND RAPIDS, MI&nbsp; 49548<br><a href=\"mailto:<EMAIL>\"><EMAIL></a></blockquote></blockquote></blockquote>Once all required documentation has been received, your request will be completed within 10 business days. If you have any questions, please contact us at ************.<br><br><br>Sincerely,<br>Consumers Energy<br><p>Please do not reply to this automated email message</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "20220112-162307", "updatedAt": {"$date": "2022-12-12T20:22:15.048Z"}, "createdAt": {"$date": "2022-01-14T21:02:44.392Z"}, "__v": 0}, {"_id": {"$oid": "61d4d2dda2f90c937d05e684"}, "templateName": "sms-loa-template", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {service-offering-title} Information Form. Please click the link below to continue with your request and view documentation required.\n\nhttps://www.consumersenergy.com/-/media/CE/Documents/business/letter-of-authorization-business-customers.ashx", "isActive": true, "version": "20220112-162307", "updatedAt": {"$date": "2022-12-12T20:22:15.053Z"}, "createdAt": {"$date": "2022-01-14T21:02:44.392Z"}, "__v": 0}, {"_id": {"$oid": "61cb95bee0a5d554dee723ed"}, "templateName": "email-hhc-template", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/top_hhc.jpg\" width=\"960\" height=\"238\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>Thank you for calling Consumers Energy to request a {service-offering-title} at the service address of <font color=\"#004993\">{service-address}</font>.</p><p>Please click the links below to continue with your request and view documentation required. Visit&nbsp;<br><br><span style=\"background-color: transparent;\"><a href=\"https://www.michigan.gov/documents/taxes/MI-1040CR7_745688_7.pdf\" target=\"_blank\" rel=\"noopener noreferrer\">2021 MICHIGAN Home Heating Credit Claim MI-1040CR-7 [michigan.gov]</a><br><br><a href=\"https://www.michigan.gov/documents/taxes/BOOK_MI-1040CR-7_745779_7.pdf\" target=\"_blank\" rel=\"noopener noreferrer\">2021 Book MI-1040CR7 Home Heating Instructions (michigan.gov) [michigan.gov]</a><br></span></p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><ul><li><span style=\"background-color: transparent;\">The MI-1040CR-7 form can be completed electronically or through the mail</span></li><ul><li><span style=\"background-color: transparent;\">Self-serve instructions for the form can be found online under “Forms and Instructions”</span></li></ul><li><span style=\"background-color: transparent;\">To obtain forms, get help filling out a form, or check on your credit with the State of Michigan - 517-636-4486</span></li><li><span style=\"background-color: transparent;\">Your heating costs can be found on your January – June bills month, your eservices account, or by contacting Consumers Energy at **************</span></li></ul></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b style=\"background-color: transparent;\"><br></b></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><b style=\"background-color: transparent;\"><br></b></blockquote><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"></blockquote><b style=\"background-color: transparent;\">The State of Michigan's deadline for filing is September 30 each year.</b><br><br><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"></blockquote>Sincerely,<br><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><p><br></p></blockquote>Consumers Energy<br><p>Please do not respond to this automated message</p></td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "20220112-162307", "updatedAt": {"$date": "2022-12-12T20:22:15.055Z"}, "createdAt": {"$date": "2022-01-21T22:24:51.856Z"}, "__v": 0}, {"_id": {"$oid": "61cb95bee0a5d554dee723ec"}, "templateName": "sms-hhc-template", "templateHtml": "Thank you for calling Consumers Energy. Attached is your request for {service-offering-title} Information Form. Please click the links below to continue with your request and view documentation required.\n\nhttps://www.michigan.gov/documents/taxes/MI-1040CR7_745688_7.pdf\n\nhttps://www.michigan.gov/documents/taxes/BOOK_MI-1040CR-7_745779_7.pdf", "isActive": true, "version": "20220112-162307", "updatedAt": {"$date": "2022-12-12T20:22:15.057Z"}, "createdAt": {"$date": "2022-01-21T22:24:51.856Z"}, "__v": 0}, {"_id": {"$oid": "6001bf4261041657401b1f84"}, "templateName": "sms-id-theft", "templateHtml": "Thank you for calling Consumers Energy to report a claim of identify theft. Please click the link below to continue with your request and view documentation required.\n\n{url}", "isActive": true, "version": "20210115-160000", "updatedAt": {"$date": "2022-12-12T20:22:15.060Z"}, "createdAt": {"$date": "2022-08-31T22:41:55.255Z"}, "__v": 0}, {"_id": {"$oid": "6001beaa61041657401b1f83"}, "templateName": "email-id-theft", "templateHtml": "<div style=\"width: 960px;\">\n\t<table style=\"width: 960px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/id-theft-header.png\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 960px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\"><br></td>\n\t\t\t\t\t\t\t\t\t\t<td><br></td></tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>Thank you for calling Consumers Energy to report a claim of identify theft to dispute charges at the service address of <font color=\"#004993\">{service-address}</font>.</p><p><span style=\"background-color: transparent;\">Before Consumers Energy can open an investigation of ID Theft, the following documentation will be required:</span><br></p><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><font size=\"5\" color=\"#004993\">❶</font>&nbsp;<span style=\"background-color: transparent;\">A submitted and notarized Federal Trade Commission (FTC) Identity Theft Report Form, which can be obtained at <a href=\"https://www.identitytheft.gov\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.identitytheft.gov</a><br></span><font size=\"5\" color=\"#004993\">❷</font>&nbsp;<span style=\"background-color: transparent;\">Copy (front and back) of a valid government-issued photo-identification card. Examples: Current driver’s license, state identification card or passport<br></span><font size=\"5\" color=\"#004993\">❸</font>&nbsp;<span style=\"background-color: transparent;\">Proof of residency for the time-frame in dispute. Examples: Rental/lease agreements, utility bills, paystubs, insurance documents, tax forms, bank statements.<br></span><span style=\"color: rgb(0, 73, 147); font-size: x-large;\">❹&nbsp;</span><span style=\"background-color: transparent;\">Filed police report of Identity Theft. If you are not able to obtain a copy of the report, please provide report number, date and location claim was filed, and officer’s name and contact information</span></blockquote><p>To continue with your claim, please visit&nbsp;<a href=\"https://www.consumersenergy.com/community/safety/identity-theft\" target=\"_blank\" rel=\"noopener noreferrer\">https://www.consumersenergy.com/community/safety/identity-theft</a>.<br></p><p>Once all required documentation has been received, a Consumers Energy investigator will contact you within 2-3 business days to confirm receipt and provide further instructions if needed. Please note that an investigation time frame may vary depending on the possibility of criminal prosecution.</p><p><br></p><p>Sincerely,</p><p>Consumers Energy</p><p><b>Replies to this email are not monitored.&nbsp; Please do not respond to this automated message.&nbsp;</b></p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 960px;height: 20px;\"><br></div>", "isActive": true, "version": "20210210-171535", "updatedAt": {"$date": "2022-12-12T20:22:15.063Z"}, "createdAt": {"$date": "2022-08-31T22:41:55.255Z"}, "__v": 0}, {"_id": {"$oid": "5faaff7604865f5b0c7e7a8c"}, "templateName": "email-past-due-TemplateC", "templateHtml": "<div style=\"width: 799px;\">\n\t<table style=\"width: 799px;\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td>\n\t\t\t\t\t<img src=\"app-api/images/stored/past-due-header.png\"></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t</p><div style=\"text-align: justify;margin-top: -70px;\">\n\t\t\t\t\t\t\t<table style=\"width: 799px; text-align: start;\">\n\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\">Account Number:</td>\n\t\t\t\t\t\t\t\t\t\t<td>xxxx xxxx {accountNumber}</td>\n\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t<td style=\"width: 150px;\">Service Address:</td>\n\t\t\t\t\t\t\t\t\t\t<td>{customerAddress}</td>\n\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t<b style=\"color: #004993;\">Dear {customerName},</b></p>\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t</p><p>We know many of our customers have been affected by the global pandemic of Covid-19. We want you to know we are here to&nbsp;help during these uncertain times.</p>\n\t\t\t\t\t\t\t\t<p>Our records indicate that your account balance of <b style=\"color: #004993;\">${amountDue}</b> is more than 90 days past due, and you could be eligible&nbsp;for shut off in the future. To make a payment online, visit our website at <a href=\"https://www.consumersenergy.com/make-a-payment-guest\">https://www.consumersenergy.com/make-a-payment-guest</a>. If you have&nbsp;already made a payment, thank you and please disregard this message.</p>\n\t\t\t\t\t\t\t\t<p>If you need assistance, we have a variety of payment arrangement options available to you. To learn more about these options&nbsp;that may be available to you in order to avoid potential shut-off in the future, please visit <a href=\"www.ConsumersEnergy.com/assistance\">www.ConsumersEnergy.com/assistance</a> or call 1-************.</p>\n\t\t\t\t\t\t\t\t<p>For residential customers, help may be available through agencies in your area. To connect with resources, please visit&nbsp;<a href=\"https://www.mi211.org\" target=\"_blank\">https://www.mi211.org</a>, or apply directly for State Emergency Relief assistance with the Michigan Department of Health and&nbsp;Human Services, by visiting <a href=\"www.Michigan.gov/mibridges\">www.Michigan.gov/mibridges</a>.</p>\n\t\t\t\t\t\t\t\t\t<p>Sincerely,</p><p>Consumers Energy</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</div><div style=\"background: #3b68ae;width: 799px;height: 20px;\"></div><span style=\"\n    font-size: 10px;\n\">Please do not reply to this automated message</span>", "isActive": true, "version": "20201110-213400", "appName": "past-due", "updatedAt": {"$date": "2022-12-12T20:22:15.066Z"}, "createdAt": {"$date": "2021-04-26T06:53:26.873Z"}, "__v": 0}, {"_id": {"$oid": "5f74562c48aa5d375c3577fc"}, "templateName": "page-asp-tos", "templateHtml": "<html>\n    <head>\n      <title>\n        Consumers Energy Company Informed Customer Consent Authorization Form\n      </title>\n      <link\n        rel=\"stylesheet\"\n        href=\"https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,700italic,300,400,700e\"\n      />\n      <script src=\"{scriptUrl}\"></script>\n  \n      <style>\n        body {\n          font-family: 'Open Sans', sans-serif;\n          overflow: hidden;\n        }\n  \n        body p {\n          font-weight: 300; /* You specify the number you see next to the fonts to change from light to bold ie you would put 700 etc. etc. */\n        }\n  \n        button {\n          font-weight: 300; /* You specify the number you see next to the fonts to change from light to bold ie you would put 700 etc. etc. */\n        }\n  \n        .body-container {\n          max-width: 800px;\n          margin: 0 auto;\n          text-align: justify;\n          white-space: normal;\n          display: flex;\n          flex-direction: column;\n        }\n  \n        button {\n          background: #2966b0;\n          border: 0;\n          padding: 10px;\n          color: white;\n          border-radius: 5px;\n          cursor: pointer;\n          box-shadow: 2px 2px 4px #888888;\n          outline: none;\n          width: 150px;\n        }\n        button:disabled {\n          background: gray;\n          cursor: no-drop;\n        }\n        button:active:hover {\n          box-shadow: none;\n        }\n        /* The container */\n        .container {\n          display: block;\n          position: relative;\n          padding-left: 30px;\n          margin-bottom: 12px;\n          cursor: pointer;\n          font-size: 22px;\n          user-select: none;\n          text-align: justify;\n          margin: 0 30px;\n        }\n  \n        /* Hide the browser's default radio button */\n        .container input {\n          position: absolute;\n          opacity: 0;\n          cursor: pointer;\n        }\n  \n        /* Create a custom radio button */\n        .checkmark {\n          position: absolute;\n          top: 0;\n          left: 0;\n          height: 25px;\n          width: 25px;\n          background-color: #cacaca;\n          border-radius: 50%;\n        }\n  \n        /* On mouse-over, add a grey background color */\n        .container:hover input ~ .checkmark {\n          background-color: #ccc;\n        }\n  \n        /* When the radio button is checked, add a blue background */\n        .container input:checked ~ .checkmark {\n          background-color: #2196f3;\n        }\n  \n        .checkbox-text {\n          color: #2196f3;\n        }\n  \n        /* Create the indicator (the dot/circle - hidden when not checked) */\n        .checkmark:after {\n          content: '';\n          position: absolute;\n          display: none;\n        }\n  \n        /* Show the indicator (dot/circle) when checked */\n        .container input:checked ~ .checkmark:after {\n          display: block;\n        }\n  \n        /* Style the indicator (dot/circle) */\n        .container .checkmark:after {\n          top: 9px;\n          left: 9px;\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background: white;\n        }\n  \n        .checkbox {\n          display: inline-block;\n          width: 12px;\n          height: 12px;\n          border: 1px solid black;\n        }\n      </style>\n    </head>\n    <body onload=\"init('{clickedUrl}', '{actionUrl}')\">\n      <div\n        style=\"height: 4px; width: 100%; background: #005bb4; margin-bottom: 1px\"\n      ></div>\n      <div\n        style=\"height: 6px; width: 100%; background: #8fbb37; margin-bottom: 20px\"\n      ></div>\n  \n      <div class=\"body-container\">\n        <img src=\"app-api/images/stored/asp-tos-top.png\" width=\"300\" style=\"margin: 18px 0 0 0; align-self: flex-end\" alt=\"\" />\n        <div>\n          <div>\n            <h3 style=\"text-align: center\">\n              Consumers Energy Company Informed Customer Consent Authorization\n              Form\n            </h3>\n            <div>\n              This document serves as formal customer consent by {firstName}\n              {lastName} for Consumers Energy Company to share certain Customer\n              Account Information and Personal Data that is identified below with\n              its Appliance Service Plan, as well as its suppliers or contractors.\n            </div>\n            <br />\n            <div style=\"text-align: center; font-style: italic\">\n              Data Privacy Policy\n            </div>\n            <br />\n            <div style=\"font-style: italic\">\n              To comply with State of Michigan information sharing policies and\n              rules, Consumers Energy Company has in place a Customer Data Privacy\n              Policy, along with a Michigan Public Service Commission-approved\n              Data Privacy Tariff to ensure the protection of consumption data,\n              Customer Account Information, and Personal Data. The goal is to\n              strike a reasonable balance between the collection, use or\n              disclosure of any customer information by Consumers Energy while\n              providing safe and reliable services. This includes services\n              provided by Consumers Energy’s unregulated entities or affiliates,\n              complying with legislative and regulatory mandates, and meeting\n              customers’ expectations regarding the collection, disclosure and use\n              of their customer account information and personal data.\n            </div>\n            <div>&nbsp;</div>\n            <div style=\"font-style: italic\">\n              By agreeing to the Terms and Conditions, you are hereby providing\n              informed customer consent for Consumers Energy to share certain\n              Customer Account Information and Personal Data for the primary\n              utility account owner. This information includes customer name,\n              address, business partner and contract account numbers, service\n              address code, and safety information on behalf of Consumers Energy\n              employees and contractors.\n            </div>\n            <div>&nbsp;</div>\n            <div style=\"font-style: italic\">\n              This informed customer consent covers information that is required\n              for the enrollment, servicing and billing of the Appliance Service\n              Plan. If the primary utility account owner or authorized adult on\n              the account does not provide informed customer consent, we will be\n              unable to provide you with the Appliance Service Plan.\n            </div>\n            <div>&nbsp;</div>\n            <div style=\"font-style: italic\">\n              Your information will be shared as often as daily between Consumers\n              Energy and the Appliance Service Plan, as well as it’s suppliers or\n              contractors. It may also be shared with others including requesting\n              competitors when required by law or state rules.\n            </div>\n            <div>&nbsp;</div>\n            <div style=\"font-style: italic\">\n              This informed customer consent authorization for your Appliance\n              Service Plan contract is valid through Dec. 31, 2030. This informed\n              customer consent may be revoked by the primary utility account owner\n              or authorized adult on the account; however, your Appliance Service\n              Plan contract will be cancelled as a result. Data used for billing\n              such as contract account number and business partner number will be\n              utilized by the Appliance Service Plan until completion of final\n              billing and payment for the program. If you would like to revoke\n              consent or if you have questions regarding consent, please call\n              **************.\n            </div>\n            <div>&nbsp;</div>\n          </div>\n        </div>\n        <div>&nbsp;</div>\n        <strong>\n          Please check the box below that indicates your informed customer\n          consent. By providing informed customer consent, you agree to these\n          terms in full.\n        </strong>\n        <div>&nbsp;</div>\n        <span onclick=\"postAnswer()\"\n          ><div\n            class=\"checkbox\"\n            style=\"cursor: pointer\"\n            onclick=\"postAnswer()\"\n          ></div\n        ></span>\n        <div\n          style=\"font-weight: bold; max-width: 19rem; display: inline-flex\"\n          id=\"checkbox-confirm\"\n          for=\"checkbox-confirm\"\n        >\n          I want to provide informed consent in order to start my Appliance\n          Service Plan coverage.\n        </div>\n        <div>&nbsp;</div>\n        <div style=\"text-align: right\">\n          <span style=\"background-color: transparent; font-size: 0.8rem\"\n            >ASP-SCC-00079-Rev.0-6/1/2021</span\n          >\n        </div>\n        <div>&nbsp;</div>\n        <div>&nbsp;</div>\n        <div>&nbsp;</div>\n        <div>&nbsp;</div>\n        <div>&nbsp;</div>\n      </div>\n    </body>\n  </html>\n  ", "isActive": true, "version": "********-1100", "updatedAt": {"$date": "2022-12-12T20:22:15.068Z"}, "createdAt": {"$date": "2020-09-30T00:00:00.000Z"}, "__v": 0}, {"_id": {"$oid": "5f468cadada1f32884835b71"}, "templateName": "sms-past-due-TemplateB", "templateHtml": "Consumers Energy: Are you getting a tax refund?  Why not use it to take care of this bill? \n\nAccount: {accountStreetAddress}\nPast Due Amount: ${amountDue}\n\nYour account appears to be past due. To view an important notice regarding your past due balance, click here: {url}\n\nTo Pay Online now, visit http://www.consumersenergy.com/make-a-payment-guest or call  **************. If you’ve already made a payment, thank you and please disregard this message.", "isActive": true, "version": "********-145822", "appName": "past-due", "updatedAt": {"$date": "2022-12-12T20:22:15.071Z"}, "createdAt": {"$date": "2020-08-26T16:30:00.000Z"}, "__v": 0}, {"_id": {"$oid": "5f468ca6ada1f32884835b70"}, "templateName": "sms-past-due-TemplateA", "templateHtml": "Consumers Energy is trying to restore natural gas service to your property.  Please call 1-888-450-9054 to schedule reconnection. Your service will remain off until an appointment is scheduled.", "isActive": true, "version": "********-1630", "appName": "past-due", "updatedAt": {"$date": "2022-12-12T20:22:15.073Z"}, "createdAt": {"$date": "2020-08-26T16:30:00.000Z"}, "__v": 0}, {"_id": {"$oid": "5ee7bc9851f75323fc57666f"}, "templateName": "email-asp-program-manual", "templateHtml": "<div style=\"width: 790px;\">\n<div style=\"height:4px;width:100%; background:#005bb4;margin-bottom:1px;\"></div><div style=\"height:6px;width:100%; background:#8fbb37;margin-bottom:20px;\"></div>\n<div style=\"text-align: right;\"><img src=\"app-api/images/stored/asp-tos-top.png\" width=\"300\" style=\"background-color: transparent;\"><span style=\"background-color: transparent;\"> ​</span></div> <table=\"\" style=\"width: 790px;\"><div style=\"text-align: justify;\"><strong style=\"background-color: transparent;\"><br></strong></div><div style=\"text-align: justify;\"><strong style=\"background-color: transparent;\"><br></strong></div><div style=\"text-align: justify;\"><strong style=\"background-color: transparent;\">Dear {firstName}&nbsp;</strong><strong style=\"background-color: transparent;\">{lastName}</strong><strong style=\"background-color: transparent;\">,&nbsp;</strong></div><div style=\"text-align: justify;\"><br></div><p style=\"text-align: justify;\">Thank you for your interest in the Appliance Service Plan. As we previously discussed in our phone conversation, before accessing your customer information and before being enrolled into our Appliance Service Plan, we will need your agreement to our customer privacy statements.</p><p style=\"text-align: justify;\">The Appliance Service Plan is requesting your consent to share your Consumers Energy Customer Account Information and Personal Data (i.e., name, address, and account information) with the ASP Program including our contractors/suppliers, so that we can provide you with world class service.<br></p><p><b>Providing Consent</b></p>You can provide your consent in two easy steps:<ol><li>Read the details by clicking the link below within 10 business days</li><li>Check the box to Accept consent option at the bottom of the web page<br></li></ol><a href=\"{url}\">REVIEW DETAILS AND CONSENT OPTIONS</a><br><p>The full Terms and Conditions are always available at www.ApplianceServicePlan.com.</p><p>We look forward to serving your needs for years to come.</p><br>Sincerely,<br><img src=\"app-api/images/stored/Zachary_DeFrain_sig-1C.jpg\" width=\"300\"><br>Zachary DeFrain<br>Program Manager, Appliance Service Plan<br><br><div style=\"text-align: center;\"><span style=\"font-size: x-small; vertical-align: sub; background-color: transparent;\">You have 10 days after enrollment to cancel without penalty. The Appliance Service plan is not regulated by the Michigan public Service Commission.</span></div><div style=\"text-align: center;\"><span style=\"font-size: x-small; vertical-align: sub; background-color: transparent;\">Coverage begins 15 days after enrollment. Enrollment does not affect your utility rates and conditions of service. Similar services may be available from other&nbsp;providers.</span></div></table=\"\"></div><div style=\"width: 790px;\"><div style=\"text-align: right;\"><span style=\"background-color: transparent;\"><font size=\"1\">ASP-SCC-00078-Rev.0-6/2/2021</font></span></div><table=\"\" style=\"width: 790px;\"><br></table=\"\"></div>", "isActive": true, "version": "********-1100", "updatedAt": {"$date": "2022-12-12T20:22:15.076Z"}, "createdAt": {"$date": "2020-06-15T19:38:02.702Z"}, "__v": 0}, {"_id": {"$oid": "5ecebbc046b4ee2f54ee7c87"}, "templateName": "email-asp-program", "templateHtml": "<div style=\"width: 790px;\">\n<div style=\"height:4px;width:100%; background:#005bb4;margin-bottom:1px;\"></div><div style=\"height:6px;width:100%; background:#8fbb37;margin-bottom:20px;\"></div>\n<div style=\"text-align: right;\"><img src=\"app-api/images/stored/asp-tos-top.png\" width=\"300\" style=\"background-color: transparent;\"><span style=\"background-color: transparent;\"> ​</span></div> <table=\"\" style=\"width: 790px;\"><div style=\"text-align: justify;\"><strong style=\"background-color: transparent;\"><br></strong></div><div style=\"text-align: justify;\"><strong style=\"background-color: transparent;\"><br></strong></div><div style=\"text-align: justify;\"><strong style=\"background-color: transparent;\">Dear {firstName}&nbsp;</strong><strong style=\"background-color: transparent;\">{lastName}</strong><strong style=\"background-color: transparent;\">,&nbsp;</strong></div><div style=\"text-align: justify;\"><br></div><p style=\"text-align: justify;\">Thank you for your interest in the Appliance Service Plan. As we previously discussed in our phone conversation, before accessing your customer information and before being enrolled into our Appliance Service Plan, we will need your agreement to our customer privacy statements.</p><p style=\"text-align: justify;\">The Appliance Service Plan is requesting your consent to share your Consumers Energy Customer Account Information and Personal Data (i.e., name, address, and account information) with the ASP Program including our contractors/suppliers, so that we can provide you with world class service.<br></p><p><b>Providing Consent</b></p>You can provide your consent in two easy steps:<ol><li>Read the details by clicking the link below within 10 business days</li><li>Check the box to Accept consent option at the bottom of the web page<br></li></ol><a href=\"{url}\">REVIEW DETAILS AND CONSENT OPTIONS</a><br><p>The full Terms and Conditions are always available at www.ApplianceServicePlan.com.</p><p>We look forward to serving your needs for years to come.</p><br>Sincerely,<br><img src=\"app-api/images/stored/Zachary_DeFrain_sig-1C.jpg\" width=\"300\"><br>Zachary DeFrain<br>Program Manager, Appliance Service Plan<br><br><div style=\"text-align: center;\"><span style=\"font-size: x-small; vertical-align: sub; background-color: transparent;\">You have 10 days after enrollment to cancel without penalty. The Appliance Service plan is not regulated by the Michigan public Service Commission.</span></div><div style=\"text-align: center;\"><span style=\"font-size: x-small; vertical-align: sub; background-color: transparent;\">Coverage begins 15 days after enrollment. Enrollment does not affect your utility rates and conditions of service. Similar services may be available from other&nbsp;providers.</span></div></table=\"\"></div><div style=\"width: 790px;\"><div style=\"text-align: right;\"><span style=\"background-color: transparent;\"><font size=\"1\">ASP-SCC-00078-Rev.0-6/2/2021</font></span></div><table=\"\" style=\"width: 790px;\"><br></table=\"\"></div>", "isActive": true, "version": "********-1100", "updatedAt": {"$date": "2022-12-12T20:22:15.079Z"}, "createdAt": {"$date": "2020-03-23T12:38:02.702Z"}, "__v": 0}, {"_id": {"$oid": "5e78adaad14257b1771dc10a"}, "templateName": "email-installment-plan", "templateHtml": "<img src=\"app-api/images/stored/top-pac.png\"><table> <tbody><tr><td>Account Number:</td><td>{account-number}</td></tr> <tr><td>Service Address:</td><td>{service-address}</td></tr> </tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br><br>Thank you for calling Consumers Energy on {current-date} to make a payment arrangement on your account.&nbsp; We've completed your request for a payment arrangement of {number-payments} {payments-frequency} payments.<br><br>Your first payment of {first-amount} is due on {first-due}.<br><br>Payment Arrangement Schedule:<br>{payments}<br><font size=\"2\"><b>Please Note:</b>&nbsp;<i>This arrangement does not include any future changes.&nbsp; All additional changes must be paid in addition to your arrangement.</i></font><br><br>Please ensure you allow sufficient time for your payment to be received on or before {first-due}.&nbsp; If you are concerned about your payment posting on time, we offer several options to pay your bill that post to your account immediately! Click&nbsp;<a href=\"https://www.consumersenergy.com/residential/billing-and-payment/payment\" target=\"_blank\">here</a>&nbsp;to learn more.<br><br>Sincerely,<br><br>Consumers Energy<br><br>Please do not reply to this automated email message.<br></td><td></td></tr></tbody></table>", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.081Z"}, "createdAt": {"$date": "2020-03-23T12:38:02.702Z"}, "__v": 0}, {"_id": {"$oid": "5e78adaad14257b1771dc109"}, "templateName": "sms-installment-plan", "templateHtml": "Thank you for calling Consumers Energy to set up a payment arrangement. Your request has been completed. Click here to view your payment schedule:\n{url}", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.083Z"}, "createdAt": {"$date": "2020-03-23T12:38:02.702Z"}, "__v": 0}, {"_id": {"$oid": "5e78adaad14257b1771dc108"}, "templateName": "sms-covid-19", "templateHtml": "Consumers Energy: PAYMENT ARRANGEMENT CONFIRMATION\n\nTemplate to be defined!\n\nPay Online Now by going to https://www.consumersenergy.com/residential/billing-and-payment/payment", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.086Z"}, "createdAt": {"$date": "2020-03-23T12:38:02.702Z"}, "__v": 0}, {"_id": {"$oid": "5e78adaad14257b1771dc107"}, "templateName": "email-covid-19", "templateHtml": "<img src=\"app-api/images/stored/top-pac.png\"><table> <tbody><tr><td>Account Number:</td><td>{account-number}</td></tr> <tr><td>Service Address:</td><td>{service-address}</td></tr> </tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br><br>Template to be defined!<br><br> Sincerely,<br><br> Consumers Energy<br><br><br>Please do not reply to this automated email message.</td><td><br></td></tr></tbody></table> &nbsp;", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.089Z"}, "createdAt": {"$date": "2020-03-23T12:38:02.702Z"}, "__v": 0}, {"_id": {"$oid": "5cf53e7b2aab9efa46484bdb"}, "templateName": "email-mep-hold", "templateHtml": "<img src=\"app-api/images/stored/top-tsohc.png\"><br><table><tbody><tr><td>Account Number:</td><td>{account-number}</td></tr><tr><td>Service Address:</td><td>{service-address}</td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br><br>Thank you for calling Consumers Energy on {current-date} to make a payment arrangement on your past due account.. We’ve completed your request fora {hold-days}-day temporary shut-off hold effective immediately under a Critical Care Hold. During this time your account will be protected from shut-offs.<br><br>Your shut-off hold will expire on&nbsp;<b><font size=\"4\" color=\"#004993\">{shutoff-date}</font></b>.<br><br>You will need to make a payment on your account before this date to avoid the risk of shut-off after the hold expires.&nbsp; If you need help paying your bill, you can call 2-1-1 to learn about other program that might be able to help with your utility costs.<br><br>If you have a qualifying, documented medical emergency,&nbsp;&nbsp;<font size=\"4\" color=\"#004993\"><b>Medical Emergency Protection</b></font>&nbsp;protects you from an energy service shut-off for up to <font size=\"4\" color=\"#004993\"><b> 21</b></font> days. You can submit an additional certificate every three weeks for a total of 63 days if the medical emergency continues that long.<br><br>This programs requires you to send Consumers Energy proof from a doctor or a notice from a public health official that service shut-off will make an existing medical problem worse.&nbsp; Visit&nbsp;<a href=\"http://www.ConsumersEnergy.com/lifesupport\" target=\"_blank\">www.ConsumersEnergy.com/lifesupport</a>&nbsp;for a copy of the Medical Certification Form. The form includes directions on how to complete and submit it.<br><br><i><font size=\"2\">PLEASE NOTE: Backup generators and transportation services are not part of this program. Participation in this program does not mean your electric power will be restored sooner than others if there is a power outage from a storm or other event.</font></i><br><br>Sincerely,<br><br>Consumers Energy<br><br><br>Please do not reply to this automated email message.</td><td><br></td></tr></tbody></table>", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.092Z"}, "createdAt": {"$date": "2019-06-03T15:36:27.977Z"}, "__v": 0}, {"_id": {"$oid": "5cf53aac2aab9efa46484bda"}, "templateName": "email-spp-hold", "templateHtml": "<img src=\"app-api/images/stored/spp.png\"><br><table><tbody><tr><td>Account Number:</td><td>{account-number}</td></tr><tr><td>Service Address:</td><td>{service-address}</td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"> <tbody><tr> <td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br><br>Thank you for calling Consumers Energy on {current-date} to make a payment arrangement on your past due balance. We’ve submitted your request to enroll in the&nbsp;shut-off protection plan. To complete your plan enrollment, a down payment must be posted to your account within <span style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif; mso-fareast-font-family:Calibri;mso-fareast-theme-font:minor-latin;mso-ansi-language: EN-US;mso-fareast-language:EN-US;mso-bidi-language:AR-SA\">{hold-days}-</span><!--[if gte mso 9]><xml> <w:WordDocument> <w:View>Normal</w:View> <w:Zoom>0</w:Zoom> <w:TrackMoves/> <w:TrackFormatting/> <w:PunctuationKerning/> <w:ValidateAgainstSchemas/> <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid> <w:IgnoreMixedContent>false</w:IgnoreMixedContent> <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText> <w:DoNotPromoteQF/> <w:LidThemeOther>EN-US</w:LidThemeOther> <w:LidThemeAsian>X-NONE</w:LidThemeAsian> <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript> <w:Compatibility> <w:BreakWrappedTables/> <w:SnapToGridInCell/> <w:WrapTextWithPunct/> <w:UseAsianBreakRules/> <w:DontGrowAutofit/> <w:SplitPgBreakAndParaMark/> <w:EnableOpenTypeKerning/> <w:DontFlipMirrorIndents/> <w:OverrideTableStyleHps/> </w:Compatibility> <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel> <m:mathPr> <m:mathFont m:val=\"Cambria Math\"/> <m:brkBin m:val=\"before\"/> <m:brkBinSub m:val=\"&#45;-\"/> <m:smallFrac m:val=\"off\"/> <m:dispDef/> <m:lMargin m:val=\"0\"/> <m:rMargin m:val=\"0\"/> <m:defJc m:val=\"centerGroup\"/> <m:wrapIndent m:val=\"1440\"/> <m:intLim m:val=\"subSup\"/> <m:naryLim m:val=\"undOvr\"/> </m:mathPr></w:WordDocument> </xml><![endif]--><!--[if gte mso 9]><xml> <w:LatentStyles DefLockedState=\"false\" DefUnhideWhenUsed=\"false\" DefSemiHidden=\"false\" DefQFormat=\"false\" DefPriority=\"99\" LatentStyleCount=\"371\"> <w:LsdException Locked=\"false\" Priority=\"0\" QFormat=\"true\" Name=\"Normal\"/> <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" Name=\"heading 1\"/> <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 2\"/> <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 3\"/> <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 4\"/> <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 5\"/> <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 6\"/> <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 7\"/> <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 8\"/> <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"heading 9\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index 4\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index 5\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index 6\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index 7\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index 8\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index 9\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toc 1\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toc 2\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toc 3\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toc 4\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toc 5\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toc 6\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toc 7\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toc 8\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toc 9\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Normal Indent\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"footnote text\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"annotation text\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"header\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"footer\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"index heading\"/> <w:LsdException Locked=\"false\" Priority=\"35\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"caption\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"table of figures\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"envelope address\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"envelope return\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"footnote reference\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"annotation reference\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"line number\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"page number\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"endnote reference\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"endnote text\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"table of authorities\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"macro\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"toa heading\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Bullet\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Number\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List 4\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List 5\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Bullet 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Bullet 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Bullet 4\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Bullet 5\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Number 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Number 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Number 4\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Number 5\"/> <w:LsdException Locked=\"false\" Priority=\"10\" QFormat=\"true\" Name=\"Title\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Closing\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Signature\"/> <w:LsdException Locked=\"false\" Priority=\"1\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Default Paragraph Font\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Body Text\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Body Text Indent\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Continue\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Continue 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Continue 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Continue 4\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"List Continue 5\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Message Header\"/> <w:LsdException Locked=\"false\" Priority=\"11\" QFormat=\"true\" Name=\"Subtitle\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Salutation\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Date\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Body Text First Indent\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Body Text First Indent 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Note Heading\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Body Text 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Body Text 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Body Text Indent 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Body Text Indent 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Block Text\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Hyperlink\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"FollowedHyperlink\"/> <w:LsdException Locked=\"false\" Priority=\"22\" QFormat=\"true\" Name=\"Strong\"/> <w:LsdException Locked=\"false\" Priority=\"20\" QFormat=\"true\" Name=\"Emphasis\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Document Map\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Plain Text\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"E-mail Signature\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Top of Form\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Bottom of Form\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Normal (Web)\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Acronym\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Address\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Cite\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Code\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Definition\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Keyboard\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Preformatted\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Sample\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Typewriter\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"HTML Variable\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Normal Table\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"annotation subject\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"No List\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Outline List 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Outline List 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Outline List 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Simple 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Simple 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Simple 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Classic 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Classic 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Classic 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Classic 4\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Colorful 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Colorful 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Colorful 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Columns 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Columns 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Columns 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Columns 4\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Columns 5\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Grid 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Grid 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Grid 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Grid 4\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Grid 5\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Grid 6\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Grid 7\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Grid 8\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table List 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table List 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table List 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table List 4\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table List 5\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table List 6\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table List 7\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table List 8\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table 3D effects 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table 3D effects 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table 3D effects 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Contemporary\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Elegant\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Professional\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Subtle 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Subtle 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Web 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Web 2\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Web 3\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Balloon Text\"/> <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"Table Grid\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Table Theme\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" Name=\"Placeholder Text\"/> <w:LsdException Locked=\"false\" Priority=\"1\" QFormat=\"true\" Name=\"No Spacing\"/> <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading\"/> <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List\"/> <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid\"/> <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1\"/> <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2\"/> <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1\"/> <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2\"/> <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1\"/> <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2\"/> <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3\"/> <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List\"/> <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading\"/> <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List\"/> <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid\"/> <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 Accent 1\"/> <w:LsdException Locked=\"false\" SemiHidden=\"true\" Name=\"Revision\"/> <w:LsdException Locked=\"false\" Priority=\"34\" QFormat=\"true\" Name=\"List Paragraph\"/> <w:LsdException Locked=\"false\" Priority=\"29\" QFormat=\"true\" Name=\"Quote\"/> <w:LsdException Locked=\"false\" Priority=\"30\" QFormat=\"true\" Name=\"Intense Quote\"/> <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 Accent 2\"/>   <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"60\" Name=\"Light Shading Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"61\" Name=\"Light List Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"62\" Name=\"Light Grid Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"63\" Name=\"Medium Shading 1 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"64\" Name=\"Medium Shading 2 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"65\" Name=\"Medium List 1 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"66\" Name=\"Medium List 2 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"67\" Name=\"Medium Grid 1 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"68\" Name=\"Medium Grid 2 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"69\" Name=\"Medium Grid 3 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"70\" Name=\"Dark List Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"71\" Name=\"Colorful Shading Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"72\" Name=\"Colorful List Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"73\" Name=\"Colorful Grid Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"19\" QFormat=\"true\" Name=\"Subtle Emphasis\"/> <w:LsdException Locked=\"false\" Priority=\"21\" QFormat=\"true\" Name=\"Intense Emphasis\"/> <w:LsdException Locked=\"false\" Priority=\"31\" QFormat=\"true\" Name=\"Subtle Reference\"/> <w:LsdException Locked=\"false\" Priority=\"32\" QFormat=\"true\" Name=\"Intense Reference\"/> <w:LsdException Locked=\"false\" Priority=\"33\" QFormat=\"true\" Name=\"Book Title\"/> <w:LsdException Locked=\"false\" Priority=\"37\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" Name=\"Bibliography\"/> <w:LsdException Locked=\"false\" Priority=\"39\" SemiHidden=\"true\" UnhideWhenUsed=\"true\" QFormat=\"true\" Name=\"TOC Heading\"/> <w:LsdException Locked=\"false\" Priority=\"41\" Name=\"Plain Table 1\"/> <w:LsdException Locked=\"false\" Priority=\"42\" Name=\"Plain Table 2\"/> <w:LsdException Locked=\"false\" Priority=\"43\" Name=\"Plain Table 3\"/> <w:LsdException Locked=\"false\" Priority=\"44\" Name=\"Plain Table 4\"/> <w:LsdException Locked=\"false\" Priority=\"45\" Name=\"Plain Table 5\"/> <w:LsdException Locked=\"false\" Priority=\"40\" Name=\"Grid Table Light\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"Grid Table 1 Light\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 Dark\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"Grid Table 6 Colorful\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"Grid Table 7 Colorful\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"Grid Table 1 Light Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 Dark Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"Grid Table 6 Colorful Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"Grid Table 7 Colorful Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"Grid Table 1 Light Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 Dark Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"Grid Table 6 Colorful Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"Grid Table 7 Colorful Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"Grid Table 1 Light Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 Dark Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"Grid Table 6 Colorful Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"Grid Table 7 Colorful Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"Grid Table 1 Light Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 Dark Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"Grid Table 6 Colorful Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"Grid Table 7 Colorful Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"Grid Table 1 Light Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 Dark Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"Grid Table 6 Colorful Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"Grid Table 7 Colorful Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"Grid Table 1 Light Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"Grid Table 2 Accent 6\"/>   <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"Grid Table 3 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"Grid Table 4 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"Grid Table 5 Dark Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"Grid Table 6 Colorful Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"Grid Table 7 Colorful Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"List Table 1 Light\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 Dark\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"List Table 6 Colorful\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"List Table 7 Colorful\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"List Table 1 Light Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 Dark Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"List Table 6 Colorful Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"List Table 7 Colorful Accent 1\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"List Table 1 Light Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 Dark Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"List Table 6 Colorful Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"List Table 7 Colorful Accent 2\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"List Table 1 Light Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 Dark Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"List Table 6 Colorful Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"List Table 7 Colorful Accent 3\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"List Table 1 Light Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 Dark Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"List Table 6 Colorful Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"List Table 7 Colorful Accent 4\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"List Table 1 Light Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 Dark Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"List Table 6 Colorful Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"List Table 7 Colorful Accent 5\"/> <w:LsdException Locked=\"false\" Priority=\"46\" Name=\"List Table 1 Light Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"47\" Name=\"List Table 2 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"48\" Name=\"List Table 3 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"49\" Name=\"List Table 4 Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"50\" Name=\"List Table 5 Dark Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"51\" Name=\"List Table 6 Colorful Accent 6\"/> <w:LsdException Locked=\"false\" Priority=\"52\" Name=\"List Table 7 Colorful Accent 6\"/> </w:LatentStyles> </xml><![endif]--><!--[if gte mso 10]> <style> /* Style Definitions */ table.MsoNormalTable {mso-style-name:\"Table Normal\"; mso-tstyle-rowband-size:0; mso-tstyle-colband-size:0; mso-style-noshow:yes; mso-style-priority:99; mso-style-parent:\"\"; mso-padding-alt:0in 5.4pt 0in 5.4pt; mso-para-margin:0in; mso-para-margin-bottom:.0001pt; mso-pagination:widow-orphan; font-size:10.0pt; font-family:\"Times New Roman\",serif;} </style> <![endif]-->days.<br><br>Your payment in the amount of&nbsp;<b><font size=\"4\" color=\"#004993\">{amount-due}</font></b>&nbsp;is due on or before&nbsp;<b><font size=\"4\" color=\"#004993\">{shutoff-date}</font></b>.<br><br>Please ensure you allow sufficient time for your payment to be received on or before {shutoff-date}.&nbsp; If you are concerned about your payment posting on time, we offer several options to pay your bill that post to your account immediately! Click&nbsp;<a href=\"https://www.consumersenergy.com/residential/billing-and-payment/payment\" target=\"_blank\">here</a>&nbsp;to learn more.<br><br>Sincerely,<br><br>Consumers Energy<br><br><br>Please do not reply to this automated email message. </td> <td><br></td> </tr> </tbody></table>", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.095Z"}, "createdAt": {"$date": "2019-06-03T15:20:12.363Z"}, "__v": 0}, {"_id": {"$oid": "5cf51a7f3556629b24238576"}, "templateName": "sms-mep-hold", "templateHtml": "Consumers Energy: PAYMENT ARRANGEMENT CONFIRMATION\n\nAccount: {service-address}\nHold Expiration Date: {shutoff-date}\nReason for Hold: Medical Emergency Protection\n\nThis programs requires you to send Consumers Energy proof from a doctor or a notice from a public health official that service shut-off will make an existing medical problem worse.\n\nVisit http://www.ConsumersEnergy.com/lifesupport for a copy of the Medical Certification Form. The form includes directions on how to complete and submit it.", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.098Z"}, "createdAt": {"$date": "2019-06-03T13:02:55.690Z"}, "__v": 0}, {"_id": {"$oid": "5cf519933556629b24238575"}, "templateName": "sms-spp-hold", "templateHtml": "Consumers Energy: CONFIRMATION OF SPP ENROLLMENT REQUEST\nAccount: {service-address}\nDown Payment Amount: {amount-due}\nDown Payment Due Date: {shutoff-date}\nReason for Hold: Pending Shut-Off Protection Plan Enrollment Request\n\nTo Pay Online Now, visit: https://www.consumersenergy.com/residential/billing-and-payment/payment", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.101Z"}, "createdAt": {"$date": "2019-06-03T12:58:59.770Z"}, "__v": 0}, {"_id": {"$oid": "5cf036b315bad24ceaba267a"}, "templateName": "email-care-ser", "templateHtml": "<div><img src=\"app-api/images/stored/top-tsohc.png\"><br><div><table><tbody><tr><td>Account Number:</td><td>{account-number}</td></tr><tr><td>Service Address:</td><td>{service-address}<br></td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><b><font size=\"4\" color=\"#004993\"><br>Dear {first-name} {last-name},</font></b><br><br>Thank you for calling Consumers Energy on {current-date} to make a payment arrangement on your past due account.. We’ve completed your request for a {hold-days}-day temporary shut-off hold to apply for assistance with {agency-name}.<br><br>Your shut-off hold will expire on&nbsp;<b><font size=\"4\" color=\"#004993\">{shutoff-date}</font></b>.<br><br><div><div>Once {agency-name}&nbsp;receives your application, they will contact us to extend the hold while your application is being reviewed. Don’t delay and begin the application process now by {agency-contact}.</div><div><br></div><div>To apply, you will need the following supporting documents:</div></div><blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><div><div><font size=\"5\" color=\"#004993\">\n❶</font>\nMost recent Consumers Energy bill</div></div><div><div><font size=\"5\" color=\"#004993\">\n❷</font>\nSocial Security card of applicant and social security numbers for household members</div></div><div><div><font size=\"5\" color=\"#004993\">\n❸</font>\nDriver's license of applicant</div></div><div><div><font size=\"5\" color=\"#004993\">❹</font>Proof of all household gross income (amount before deductions) received during the 30 days prior to the application signature date&nbsp;</div></div></blockquote><div><div><br></div><div>You may be eligible for more than one program! Click&nbsp;<a href=\"https://www.consumersenergy.com/residential/programs-and-services/payment-assistance#AssistanceWeOffer\" target=\"_blank\">here</a>&nbsp;to learn more about assistance options available for qualifying customers.</div></div><div><br></div><div><div>Sincerely,<br>Consumers Energy<br><br>Please do not reply to this automated email message.<br></div><div><br></div><div><br></div></div></td><td><br></td></tr></tbody></table> </div></div>", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.104Z"}, "createdAt": {"$date": "2019-05-30T20:01:55.681Z"}, "__v": 0}, {"_id": {"$oid": "5cf036a315bad24ceaba2679"}, "templateName": "sms-care-ser", "templateHtml": "Consumers Energy: CONFIRMATION OF TEMPORARY SHUT-OFF HOLD\nAccount: {service-address}\nHold Expiration Date: {shutoff-date}\nAgency Contact Information:\n{agency-name}\n{agency-contact}\nGo to https://www.consumersenergy.com/residential/programs-and-services/payment-assistance#AssistanceWeOffer to learn more about other energy assistance available to you.", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.106Z"}, "createdAt": {"$date": "2019-05-30T20:01:39.560Z"}, "__v": 0}, {"_id": {"$oid": "5cf0369015bad24ceaba2678"}, "templateName": "email-sph-hold", "templateHtml": "<img src=\"app-api/images/stored/top-pac.png\"><table> <tbody><tr><td>Account Number:</td><td>{account-number}</td></tr> <tr><td>Service Address:</td><td>{service-address}</td></tr> </tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {first-name} {last-name},</font></b><br><br> Thank you for calling Consumers Energy on {current-date} to make a payment arrangement on your past due balance. We’ve completed your request for additional time to pay your bill and have placed a {hold-days}-day {service-offering-title} extension on your account.<br> <br> Your payment in the amount of <b><font size=\"4\" color=\"#004993\"> {amount-due}</font></b> is due on or before <b><font size=\"4\" color=\"#004993\"> {shutoff-date}</font></b>.<br> <br> Please ensure you allow sufficient time for your payment to be received on or before {shutoff-date}.&nbsp; If you are concerned about your payment posting on time, we offer several options to pay your bill that post to your account immediately! Click&nbsp;<a href=\"https://www.consumersenergy.com/residential/billing-and-payment/payment\" target=\"_blank\">here</a> to learn more.<br> <br> Sincerely,<br><br> Consumers Energy<br><br><br>Please do not reply to this automated email message.</td><td><br></td></tr></tbody></table> &nbsp;", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.109Z"}, "createdAt": {"$date": "2019-05-30T20:01:20.130Z"}, "__v": 0}, {"_id": {"$oid": "5cf0353615bad24ceaba2677"}, "templateName": "sms-sph-hold", "templateHtml": "Consumers Energy: PAYMENT ARRANGEMENT CONFIRMATION\n\nAccount: {service-address}\nShut-off Amount: {amount-due}\nHold Expiration Date: {shutoff-date}\n\nPay Online Now by going to https://www.consumersenergy.com/residential/billing-and-payment/payment", "isActive": true, "version": "********-144931", "updatedAt": {"$date": "2022-12-12T20:22:15.111Z"}, "createdAt": {"$date": "2019-05-30T19:55:34.238Z"}, "__v": 0}, {"_id": {"$oid": "6362c9c94a5ab7c4b6ed45d2"}, "templateName": "email-dr-smart-therm-purchase", "templateHtml": "<img src=\"app-api/images/stored/smart_thermo.jpg\" style=\"width:960px;\"><br><div style=\"width: 960px;\"><table style=\"width: 960px;\"> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br></div><div><div><font face=\"Arial\">Hello and thank you for your interest in the Smart Thermostat Program. As discussed, please&nbsp;</font><a href=\"https://consumersenergystore.com/Peak-Power-Savers%C2%AE-Smart-Thermostats/\" target=\"_blank\">visit our Consumers Energy Online Store</a><font face=\"Arial\">&nbsp;to purchase a qualifying smart thermostat and enroll in the program at checkout.</font><br></div><div><div><br></div><div><a href=\"https://consumersenergystore.com/Peak-Power-Savers%C2%AE-Smart-Thermostats/\" target=\"_blank\" style=\"display: inline-block;background-color: #2966b0;color: white;font-size: 16px;width: 150px; height: 25px;font-weight: bold; text-decoration:none; text-align: center;border-radius: 5px;\">Shop and Enroll</a><br></div><div>&nbsp;</div><div><div><div><br></div><div>Sincerely,&nbsp;</div><div>Consumers Energy</div><div><br></div><div>Please do not reply to this automated message.</div></div></div></div></div></div>", "isActive": true, "version": "********-184304", "updatedAt": {"$date": "2022-12-23T00:06:06.096Z"}, "createdAt": {"$date": "2022-11-02T19:49:29.386Z"}, "__v": 0}, {"_id": {"$oid": "6362e7a5b79ab9ab02a45e7a"}, "templateName": "email-dr-smart-therm-enroll", "templateHtml": "<img src=\"app-api/images/stored/smart_thermo.jpg\" style=\"width:960px;\"><br><div style=\"width: 960px;\"><table style=\"width: 960px;\"> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br></div><div><div><font face=\"Arial\">Hello and thank you for your interest in the Smart Thermostat Program. As discussed, please&nbsp;</font><a href=\"https://welcome.demandresponse.consumersenergy.com/start\" target=\"_blank\">visit our website</a><font face=\"Arial\">&nbsp;to enroll your smart thermostat today.</font><br></div><div><div><br></div><div><a href=\"https://welcome.demandresponse.consumersenergy.com/start\" target=\"_blank\" style=\"display: inline-block;background-color: #2966b0;color: white;font-size: 16px;width: 150px;font-weight: bold; text-decoration:none; text-align: center;height: 25px;border-radius: 5px;\">Enroll Now</a>&nbsp;<br></div><div>&nbsp;</div><div><div><div><br></div><div>Sincerely,&nbsp;</div><div>Consumers Energy</div><div><br></div><div>Please do not reply to this automated message.</div></div></div></div></div></div>", "isActive": true, "version": "********-184304", "updatedAt": {"$date": "2022-12-23T00:06:06.100Z"}, "createdAt": {"$date": "2022-11-02T21:56:53.420Z"}, "__v": 0}, {"_id": {"$oid": "63630baeb79ab9ab02a4623a"}, "templateName": "email-dr-peak-time-rewards", "templateHtml": "<img src=\"app-api/images/stored/peak_rewards.jpg\" style=\"width:960px;\"><br><div style=\"width: 960px;\"><table style=\"width: 960px;\"> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br></div><div><div><div>Hello and thank you for joining the Peak Time Rewards Program. In the coming weeks, you will receive an official email communication welcoming you to the program and providing additional resources to help you make the most of your participation.&nbsp;<a href=\"https://www.consumersenergy.com/residential/rates/electric-rates-and-programs/rate-plan-options/time-of-use-rate-plans\" target=\"_blank\">Visit our website</a>&nbsp;to learn more.</div><div><br></div><div><br></div></div><div><div><div>Sincerely,&nbsp;</div><div>Consumers Energy</div><div><br></div><div>Please do not reply to this automated message.</div></div></div></div></div>", "isActive": true, "version": "********-184304", "updatedAt": {"$date": "2022-12-23T00:06:06.104Z"}, "createdAt": {"$date": "2022-11-03T00:30:38.148Z"}, "__v": 0}, {"_id": {"$oid": "6363f32048503041afcef169"}, "templateName": "email-dr-critical-peak-pricing", "templateHtml": "<img src=\"app-api/images/stored/critical_peak.jpg\" style=\"width:960px;\"><br><div style=\"width: 960px;\"><table style=\"width: 960px;\"> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br></div><div><div><div>Hello and thank you for joining the Critical Peak Pricing Program. In the coming weeks, you will receive an official email communication welcoming you to the program and providing additional resources to help you make the most of your participation.&nbsp;<a href=\"https://www.consumersenergy.com/residential/rates/electric-rates-and-programs/rate-plan-options/time-of-use-rate-plans\" target=\"_blank\">Visit our website</a>&nbsp;to learn more.<br></div></div><div><br></div><div><br></div><div><div><div>Sincerely,&nbsp;</div><div>Consumers Energy</div><div><br></div><div>Please do not reply to this automated message.</div></div></div></div></div>", "isActive": true, "version": "********-184304", "updatedAt": {"$date": "2022-12-23T00:06:06.107Z"}, "createdAt": {"$date": "2022-11-03T16:58:08.847Z"}, "__v": 0}, {"_id": {"$oid": "6363f73244f904e0799920e3"}, "templateName": "email-dr-ac-peak-cycling", "templateHtml": "<img src=\"app-api/images/stored/ac_peak.jpg\" style=\"width:960px;\"><br><div style=\"width: 960px;\"><table style=\"width: 960px;\"> <tbody><tr><td><br><br>Account Number:&nbsp; ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp; &nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br></div><div><div><div>Hello and thank you for joining the AC Peak Cycling Program. In the coming weeks, you will be contacted to schedule installation of your AC Peak Cycling device.&nbsp;<a href=\"https://www.consumersenergy.com/residential/rates/electric-rates-and-programs/rate-plan-options/standard-rate-plan/ac-peak-cycling?utm_source=ac&amp;utm_medium=vanity-url&amp;utm_campaign=peak%20powersavers&amp;utm_content=ac\" target=\"_blank\">Visit our website</a>&nbsp;to learn more.<br></div></div><div><br></div><div><br></div><div><div><div>Sincerely,&nbsp;</div><div>Consumers Energy</div><div><br></div><div>Please do not reply to this automated message.</div></div></div></div></div>", "isActive": true, "version": "********-184304", "updatedAt": {"$date": "2022-12-23T00:06:06.110Z"}, "createdAt": {"$date": "2022-11-03T17:15:30.692Z"}, "__v": 0}, {"_id": {"$oid": "6363f94c44f904e079992232"}, "templateName": "sms-dr-smart-therm-purchase", "templateHtml": "Message from Consumers Energy: \n\nVisit our Online store to purchase your smart thermostat and enroll in the Smart Thermostat Program: \nConsumersEnergy.com/tstatenroll\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-193115", "updatedAt": {"$date": "2022-12-12T20:22:15.126Z"}, "createdAt": {"$date": "2022-11-03T17:24:28.405Z"}, "__v": 0}, {"_id": {"$oid": "6363f9ac44f904e07999237c"}, "templateName": "sms-dr-smart-therm-enroll", "templateHtml": "Message from Consumers Energy: \n\nVisit our website to enroll your smart thermostat in the Smart Thermostat Program: \nConsumersEnergy.com/enrolltstat\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-193115", "updatedAt": {"$date": "2022-12-12T20:22:15.128Z"}, "createdAt": {"$date": "2022-11-03T17:26:04.793Z"}, "__v": 0}, {"_id": {"$oid": "6363fa5544f904e079992381"}, "templateName": "sms-dr-peak-time-rewards", "templateHtml": "Message from Consumers Energy: \n\nThank you for joining the Peak Time Rewards program. You will receive more information by email in the coming weeks about your enrollment.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-193115", "updatedAt": {"$date": "2022-12-12T20:22:15.130Z"}, "createdAt": {"$date": "2022-11-03T17:28:53.839Z"}, "__v": 0}, {"_id": {"$oid": "6363fa9f44f904e0799924cc"}, "templateName": "sms-dr-critical-peak-pricing", "templateHtml": "Message from Consumers Energy: \n\nThank you for joining the Critical Peak Pricing program. You will receive more information by email in the coming weeks about your enrollment.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-193115", "updatedAt": {"$date": "2022-12-12T20:22:15.132Z"}, "createdAt": {"$date": "2022-11-03T17:30:07.864Z"}, "__v": 0}, {"_id": {"$oid": "6363fae744f904e079992616"}, "templateName": "sms-dr-ac-peak-cycling", "templateHtml": "Message from Consumers Energy: \n\nThank you for joining the AC Peak Cycling program. You will be contacted in the coming weeks to schedule your installation.\n\nPlease do not reply to this automated message.", "isActive": true, "version": "********-193115", "updatedAt": {"$date": "2022-12-12T20:22:15.135Z"}, "createdAt": {"$date": "2022-11-03T17:31:19.907Z"}, "__v": 0}, {"_id": {"$oid": "63c1aac49785d2c12b8e61b3"}, "__v": 0, "createdAt": {"$date": "2023-01-15T21:26:30.257Z"}, "isActive": true, "templateHtml": "<img src=\"app-api/images/stored/xXznSCtcATJ+qlMPCpyTUBKUPJM.png\" style=\"width:960px;\"><br><table><tbody><tr><td>Account Number:&nbsp; ending in {accountNumber:last-chars:4}</td></tr><tr><td>Service Address:&nbsp; &nbsp; {serviceAddress}</td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br><br>Thank you for calling Consumers Energy on {currentDate:dt:MM/DD/YYYY} to request {data.serviceOfferingTitle} at {serviceAddress}. We’ve submitted your request and an Energy Request Specialist will be assigned your request and contact you within 1 business day.&nbsp; If you do not receive a reply within 2 business days, call or email Energy Request Center Phone (************* or <EMAIL>.&nbsp;<br><br>Please reference your confirmation number {data.confirmNumberOther}&nbsp;when emailing or discussing this request.<br><br>Sincerely,<br><br>Consumers Energy<br><br><br>Please do not reply to this automated email message.</td><td><br></td></tr></tbody></table>", "templateName": "email-csr-service-confirmation-other", "updatedAt": {"$date": "2023-02-06T16:51:20.057Z"}, "version": "********-235028"}, {"_id": {"$oid": "63c1acb79785d2c12b8e61b9"}, "__v": 0, "createdAt": {"$date": "2023-01-15T21:26:30.263Z"}, "isActive": true, "templateHtml": "Consumers Energy Request for Services Confirmation\n\nName: {firstName} {lastName}\nAddress: {serviceAddress}\nService Request: {data.serviceOfferingTitle}\nConfirmation Number: {data.confirmNumberOther}\n\nEnergy Request Specialist will contact in 1 business day. Direct phone number is 844-3216-9537 or <EMAIL>\n\nPlease do not reply to this automated message", "templateName": "sms-csr-service-confirmation-other", "updatedAt": {"$date": "2023-02-06T16:51:20.059Z"}, "version": "********-224617"}, {"_id": {"$oid": "63c1d07eac2741efd8c08481"}, "__v": 0, "createdAt": {"$date": "2023-01-15T21:26:30.266Z"}, "isActive": true, "templateHtml": "<img src=\"app-api/images/stored/j_T4d9AUqMpSYpggT672txmTFcw.png\" style=\"width:960px;\"><br><table><tbody><tr><td>Account Number: ending in {accountNumber:last-chars:4}</td></tr><tr><td>Service Address:&nbsp; &nbsp;{serviceAddress}</td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br><br>Thank you for calling Consumers Energy on {currentDate:dt:MM/DD/YYYY}&nbsp;to start services at {serviceAddress}. {data.serviceOfferingType} Services have been placed in your name at this location effective {data.moveInDate:dt:MM/DD/YYYY}. Your confirmation number is {data.confirmNumber}.<br><br><br>{data.conditionalText:from-html}<br><br>Sincerely,<br><br>Consumers Energy<br><br><br>Please do not reply to this automated email message.</td><td><br></td></tr></tbody></table>", "templateName": "email-csr-service-confirmation-move-in", "updatedAt": {"$date": "2023-02-06T16:51:20.064Z"}, "version": "********-233957"}, {"_id": {"$oid": "63c1d35bac2741efd8c08487"}, "__v": 0, "createdAt": {"$date": "2023-01-15T21:26:30.267Z"}, "isActive": true, "templateHtml": "Consumers Energy Confirmation number for Move-in start of service\n\nName: {firstName} {lastName}\nAddress: {serviceAddress}\nDate of Service: {data.moveInDate:dt:MM/DD/YYYY}\nConfirmation: {data.confirmNumber}\n\n{data.conditionalTextSms}\n\n\nPlease do not reply to this automated message", "templateName": "sms-csr-service-confirmation-move-in", "updatedAt": {"$date": "2023-02-06T16:51:20.067Z"}, "version": "********-214230"}, {"_id": {"$oid": "63fe8e44e391e97ae6d70bde"}, "__v": 0, "createdAt": {"$date": "2023-03-01T20:04:13.358Z"}, "isActive": true, "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table><tbody><tr><td>Account Number:&nbsp;ending in {accountNumber:last-chars:4}</td></tr><tr><td>Service Address:&nbsp;{serviceAddress}</td></tr></tbody></table><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br><br>Thank you for reaching out with your concerns. We have received your complaint filed with {data.locationName}, notification number {data.notificationNumber}. I assure you Consumers Energy takes all customer complaints seriously.<br><br>Our Complaints Advocate {currentUser.firstName} {currentUser.lastName} will be in touch with you soon to discuss your concerns and gather any additional information that may be needed.&nbsp;<br><br>We appreciate your patience and understanding during this process. Please feel free to reach out to {currentUser.firstName} {currentUser.lastName} @ {currentUser.phone:phone} or {currentUser.email} if you have any further questions or concerns. Our team is dedicated to resolving your issues as soon as possible.<br><br>Sincerely,<br><br>Consumers Energy<br><br>Please do not reply to this automated message.</td><td><br></td></tr></tbody></table>", "templateName": "email-cc-cust-complaint", "updatedAt": {"$date": "2023-03-08T22:03:37.904Z"}, "version": "20230308-183402"}, {"_id": {"$oid": "63fe98ade391e97ae6d70bec"}, "__v": 0, "createdAt": {"$date": "2023-03-01T20:04:13.366Z"}, "isActive": true, "templateHtml": "\nDear {firstName} {lastName},\n\nThank you for reaching out with your concerns. We have received your complaint filed with {data.locationName}, notification number {data.notificationNumber}. I assure you Consumers Energy takes all customer complaints seriously. \n \n\nOur Complaints Advocate {currentUser.firstName} {currentUser.lastName} will be in touch with you soon to discuss your concerns and gather any additional information that may be needed. \n \n\nWe appreciate your patience and understanding during this process. Please feel free to reach out to {currentUser.firstName} {currentUser.lastName} @ {currentUser.phone:phone} or {currentUser.email} if you have any further questions or concerns. Our team is dedicated to resolving your issues as soon as possible.\n\nSincerely,\n\nConsumers Energy\n\nPlease do not reply to this automated message.", "templateName": "sms-cc-cust-complaint", "updatedAt": {"$date": "2023-03-08T22:03:37.910Z"}, "version": "********-182107"}, {"_id": {"$oid": "640137888ce6f20a9703d7fa"}, "templateName": "email-cc-freeFormLetter", "templateHtml": "<img src=\"app-api/images/stored/customer_billing.png\" style=\"width:960px;\"><br><table> <tbody><tr><td><br><br>Account Number:&nbsp;ending in {accountNumber:last-chars:4}<br>Service Address:&nbsp;{serviceAddress}<br></td><td><br></td><td></td></tr> </tbody></table><div><br></div><div><br><b><font size=\"\\&quot;4\\&quot;\" color=\"\\&quot;#004993\\&quot;\">Dear {firstName} {lastName},</font></b><br><br></div><div><div><div><font face=\"Arial\">{data.letterText:to-html}</font></div></div><div><font face=\"Arial\"><font face=\"Arial\"><br></font></font></div><div><div><div><font face=\"Arial\"><br></font></div><div><div><font face=\"Arial\">Sincerely,</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">Consumers Energy</font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\"><a href=\"\\&quot;https://www.consumersenergy.com\" target=\"\\&quot;_blank\\&quot;\">www.consumersenergy.com</a></font></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\">Please do not reply to this automated message.</font></div></div></div></div><div><font face=\"Arial\"><br></font></div><div><font face=\"Arial\"><br></font></div></div>", "isActive": true, "version": "20230308-183434", "createdAt": {"$date": "2023-03-02T23:55:52.082Z"}, "updatedAt": {"$date": "2023-03-08T22:03:37.914Z"}, "__v": 0}, {"_id": {"$oid": "6401396d8ce6f20a9703d804"}, "templateName": "sms-cc-freeFormLetter", "templateHtml": "Consumers Energy\n\n{data.letterText}\n\n\nPlease do not reply to this automated message.\n", "isActive": true, "version": "20230303-000357", "createdAt": {"$date": "2023-03-03T00:03:57.933Z"}, "updatedAt": {"$date": "2023-03-08T22:03:37.917Z"}, "__v": 0}, {"_id": {"$oid": "677e78b1391cca718093c17e"}, "templateName": "email-csr-interaction-links", "templateHtml": "<img src=\"app-api/images/customer-service-summary.png\" style=\"width:960px;\">\n<br><table width=\"100%\" cellspacing=\"0\" border=\"0\"><tbody><tr><td width=\"950\"><br><b><font size=\"4\" color=\"#004993\">Dear {firstName} {lastName},</font></b><br><br>\nThank you for contacting Consumers Energy today.<br><br>  \nFor reference, the Interaction ID number for this contact is {data.interactionId}.<br><br>\nFor more information, please see:<br><br><br>{data.conditionalText:from-html}\n<br><br>Sincerely,<br><br>Consumers Energy<br><br><br>Please do not reply to this automated email message.</td><td><br></td></tr></tbody></table>", "isActive": true, "version": "20250108-130801", "createdAt": {"$date": "2025-01-08T13:08:01.661Z"}, "updatedAt": {"$date": "2025-01-08T13:08:01.661Z"}, "__v": 0}, {"_id": {"$oid": "677e7904391cca718093c187"}, "templateName": "sms-csr-interaction-links", "templateHtml": "Than you for contacting Consumers Energy today.\n\nFor reference the Interaction ID number for this contact is {data.interactionId}.\n\nFor more information, please see:\n\n{data.conditionalTextSms}", "isActive": true, "version": "20250108-130924", "createdAt": {"$date": "2025-01-08T13:09:24.589Z"}, "updatedAt": {"$date": "2025-01-08T13:09:24.589Z"}, "__v": 0}]