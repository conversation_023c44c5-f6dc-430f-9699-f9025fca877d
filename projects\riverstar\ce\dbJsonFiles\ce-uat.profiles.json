[{"_id": {"$oid": "6290a500cc41b488ef6b07f7"}, "profileName": "Past Due Admin", "menuItems": [{"menuTitle": "Past Due Data Extract", "menuRoute": "app/past-due/extract", "roles": ["PAST_DUE_EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "PAST_DUE_EXTRACT_CSV"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.201Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.201Z"}}, {"_id": {"$oid": "6290a500cc41b488ef6b07f8"}, "profileName": "ASP User Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.202Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.202Z"}}, {"_id": {"$oid": "6290a500cc41b488ef6b07f9"}, "profileName": "ASP_ADMIN", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.202Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.202Z"}}, {"_id": {"$oid": "6290a500cc41b488ef6b07fa"}, "profileName": "ASP_CSR", "menuItems": [{"menuTitle": "Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "ADD_ASP_CUSTOMER"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.202Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.202Z"}}, {"_id": {"$oid": "6290a500cc41b488ef6b07fb"}, "profileName": "ASP Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "User Admin", "menuRoute": "user-admin/list", "roles": ["SysAdmin"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "roles": ["EDIT_TEMPLATE"], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "ASP Search", "menuRoute": "app/asp-customer/list", "roles": ["SEARCH_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "ASP Data Extract", "menuRoute": "app/asp-customer/extract", "roles": ["EXTRACT_CSV"], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Rollup Report", "menuRoute": "app/asp-customer/rollup-rpt", "roles": ["ROLLUP_REPORT"], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin", "EDIT_TEMPLATE", "SEARCH_ASP_CUSTOMER", "SEND_MESSAGES_ASP_CUSTOMER", "RESEND_MESSAGES_ASP_CUSTOMER", "VIEW_ASP_CUSTOMER", "EXTRACT_CSV", "ADD_ASP_CUSTOMER"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.202Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.202Z"}}, {"_id": {"$oid": "6290a500cc41b488ef6b07fc"}, "profileName": "CSR", "menuItems": [{"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.202Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.202Z"}}, {"_id": {"$oid": "62bdd98c085f82352bb9b631"}, "profileName": "Electric Operations", "menuItems": [{"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.202Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.202Z"}}, {"_id": {"$oid": "62cf1e5111d300ed212025f6"}, "profileName": "Revenue Operations", "menuItems": [{"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.202Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.202Z"}}, {"_id": {"$oid": "6306b8ee2febeabdbd04498a"}, "profileName": "Customer Billing", "menuItems": [{"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/cbmm/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.202Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.202Z"}}, {"_id": {"$oid": "63fd600fc777b0fa9e9c66ca"}, "profileName": "Customer Care", "menuItems": [{"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.203Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.203Z"}}, {"_id": {"$oid": "642b4f118e74fcb96b312a33"}, "profileName": "Business Center", "menuItems": [{"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": true, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.203Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.203Z"}}, {"_id": {"$oid": "6290a500cc41b488ef6b07fe"}, "profileName": "Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "menuItems": [{"menuTitle": "Profile", "menuRoute": "profiles/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "User Admin", "menuRoute": "user-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Agencies", "menuRoute": "app/agency/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Users Upload", "menuRoute": "app/user-upload/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}], "expand": true, "elementRef": null}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "Search", "menuRoute": "app/csr/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "CSV Extract", "menuRoute": "app/reports/csv-extract", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Summary report", "menuRoute": "app/reports/shutoff-hold-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.203Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.203Z"}}, {"_id": {"$oid": "62b118d6cd46f7dab4f59f59"}, "profileName": "Super Admin", "menuItems": [{"menuTitle": "Admin", "menuRoute": "", "menuItems": [{"menuTitle": "Profile", "menuRoute": "profiles/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "User Admin", "menuRoute": "user-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Agencies", "menuRoute": "app/agency/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Users Upload", "menuRoute": "app/user-upload/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Templates", "menuRoute": "app/template-admin/list", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Usage Report", "menuRoute": "usage-report", "expand": false, "elementRef": null, "menuItems": []}], "expand": true, "elementRef": null}, {"menuTitle": "CSR Options", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "CSR Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations", "menuRoute": "app/eo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Electric Operations Search", "menuRoute": "app/eo/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations", "menuRoute": "app/rev/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Revenue Operations Search", "menuRoute": "app/rev/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Billing", "menuRoute": "app/cbmm/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Billing Search", "menuRoute": "app/cbmm/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Direct Payment Office Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Demand Response Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Customer Care", "menuRoute": "app/cc/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Customer Care Search", "menuRoute": "app/cc/list", "menuItems": [], "expand": true, "roles": []}, {"menuTitle": "Business Center", "menuRoute": "app/csr/edit/0", "menuItems": [], "expand": false, "elementRef": null, "roles": []}, {"menuTitle": "Business Center Search", "menuRoute": "app/csr/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "CSV Extract", "menuRoute": "app/reports/csv-extract", "menuItems": [], "expand": false, "elementRef": null, "roles": ["SysAdmin"]}, {"menuTitle": "Summary report", "menuRoute": "app/reports/shutoff-hold-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.203Z"}, "updatedAt": {"$date": "2023-04-05T20:35:50.833Z"}}, {"_id": {"$oid": "62e2ae593c8677ac3693cab1"}, "profileName": "<PERSON><PERSON>", "menuItems": [{"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/frt/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/frt/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.203Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.203Z"}}, {"_id": {"$oid": "6331e8754afc3a75729866b5"}, "profileName": "Direct Payment Office", "menuItems": [{"menuTitle": "Direct Payment Office", "menuRoute": "app/dpo/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dpo/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.204Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.204Z"}}, {"_id": {"$oid": "63619b162537f0b2f82fec49"}, "profileName": "Demand Response", "menuItems": [{"menuTitle": "Demand Response", "menuRoute": "app/dr/edit/0", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Search", "menuRoute": "app/dr/list", "menuItems": [], "expand": true, "roles": []}], "isActive": true, "isDefault": false, "roles": [], "menuBehavior": "Open", "defaultRoute": "", "__v": 0, "createdAt": {"$date": "2023-04-04T00:21:38.204Z"}, "updatedAt": {"$date": "2023-04-04T00:21:38.204Z"}}]